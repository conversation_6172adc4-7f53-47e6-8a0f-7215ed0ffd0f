#!/usr/bin/env python3
"""
Test script to verify As-Is illustration integration with scenarios
"""

import requests
import json
import base64
from datetime import datetime

# API Configuration
BASE_URL = "http://localhost:8000"
USERNAME = "demo"
PASSWORD = "demo123"

def create_auth_header():
    """Create Basic Auth header"""
    credentials = f"{USERNAME}:{PASSWORD}"
    encoded = base64.b64encode(credentials.encode()).decode()
    return {"Authorization": f"Basic {encoded}"}

def test_create_scenario():
    """Test creating a scenario (simulating what happens when As-Is is saved)"""
    print("🔍 Testing scenario creation...")
    
    headers = {
        "Content-Type": "application/json",
        **create_auth_header()
    }
    
    test_data = {
        "name": "Test As-Is Configuration - <PERSON> (POL-TEST-001)",
        "category": "as-is",
        "asIsDetails": "Retirement Age: 65, Maturity Age: 121",
        "whatIfOptions": [
            "Face Amount: 750,000",
            "Annual Premium: 7,500"
        ]
    }
    
    response = requests.post(
        f"{BASE_URL}/api/scenarios",
        headers=headers,
        json=test_data
    )
    
    print(f"Status: {response.status_code}")
    if response.status_code == 201:
        result = response.json()
        print(f"Response: {result}")
        return result.get("id")
    else:
        print(f"Error: {response.text}")
        return None

def test_get_scenarios():
    """Test getting all scenarios"""
    print("\n🔍 Testing scenarios retrieval...")
    
    headers = create_auth_header()
    
    response = requests.get(
        f"{BASE_URL}/api/scenarios",
        headers=headers
    )
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        scenarios = result.get('scenarios', [])
        print(f"Found {len(scenarios)} scenarios")
        
        # Filter As-Is scenarios
        as_is_scenarios = [s for s in scenarios if s.get('category') == 'as-is']
        print(f"Found {len(as_is_scenarios)} As-Is scenarios")
        
        for scenario in as_is_scenarios:
            print(f"  - {scenario['name']} (ID: {scenario['id']})")
        
        return scenarios
    else:
        print(f"Error: {response.text}")
        return []

def test_get_selected_scenarios():
    """Test getting selected scenarios"""
    print("\n🔍 Testing selected scenarios retrieval...")
    
    headers = create_auth_header()
    
    response = requests.get(
        f"{BASE_URL}/api/scenarios/selected",
        headers=headers
    )
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        selected_ids = result.get('selectedScenarios', [])
        print(f"Found {len(selected_ids)} selected scenarios")
        print(f"Selected IDs: {selected_ids}")
        return selected_ids
    else:
        print(f"Error: {response.text}")
        return []

def test_as_is_illustrations():
    """Test getting As-Is illustrations"""
    print("\n🔍 Testing As-Is illustrations retrieval...")
    
    headers = create_auth_header()
    
    response = requests.get(
        f"{BASE_URL}/api/as-is-illustrations",
        headers=headers
    )
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        illustrations = result.get('illustrations', [])
        print(f"Found {len(illustrations)} As-Is illustrations")
        
        for illustration in illustrations:
            print(f"  - {illustration['name']} (ID: {illustration['id']})")
        
        return illustrations
    else:
        print(f"Error: {response.text}")
        return []

def main():
    """Run integration tests"""
    print("🚀 Starting As-Is Integration Tests")
    print("=" * 50)
    
    # Test creating a scenario (simulates saving As-Is)
    scenario_id = test_create_scenario()
    
    # Test getting all scenarios
    scenarios = test_get_scenarios()
    
    # Test getting selected scenarios
    selected_scenarios = test_get_selected_scenarios()
    
    # Test getting As-Is illustrations
    illustrations = test_as_is_illustrations()
    
    print("\n📊 Summary:")
    print(f"  - Total scenarios: {len(scenarios)}")
    print(f"  - Selected scenarios: {len(selected_scenarios)}")
    print(f"  - As-Is illustrations: {len(illustrations)}")
    
    # Check if the new scenario was automatically selected
    if scenario_id and scenario_id in selected_scenarios:
        print("✅ New scenario was automatically selected!")
    elif scenario_id:
        print("⚠️  New scenario was created but not automatically selected")
    
    print("\n✅ Integration tests completed!")

if __name__ == "__main__":
    main()
