import React, { useState } from 'react';
import { ArrowRight, Calculator, Search, User, Shield, DollarSign, FileText, CreditCard, Activity } from 'lucide-react';
import { useDashboard } from '../../contexts/DashboardContext';
import { ApiService } from '../../services/api';
import { Customer, PolicyInfo, PolicySearchRequest } from '../../types';

// Legacy interface for compatibility
interface Policy {
  id: string;
  name: string;
  description: string;
  coverage: string;
  premium: string;
  features: string[];
}

const PolicySelection = () => {
  const [customerName, setCustomerName] = useState('');
  const [policyNumber, setPolicyNumber] = useState('');
  const [customerId, setCustomerId] = useState('');
  const [selectedPolicyIndex, setSelectedPolicyIndex] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Show notification function
  const showNotification = (message: string, isSuccess: boolean = true) => {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
      isSuccess ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    } transition-all duration-300`;
    notification.innerHTML = `
      <div class="flex items-center space-x-2">
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
        </svg>
        <span>${message}</span>
      </div>
    `;
    document.body.appendChild(notification);

    setTimeout(() => {
      notification.remove();
    }, 4000);
  };
  const {
    setActiveTab,
    setSelectedCustomerData,
    setSelectedPolicyData,
    currentCustomer,
    selectedPolicyInfo,
    policySearchPerformed,
    setCurrentCustomer,
    setSelectedPolicyInfo,
    setPolicySearchPerformed
  } = useDashboard();



  // Helper: localStorage key
  const getStorageKey = (id: string, policy: string, name: string) => `customerData:${id.trim()}|${policy.trim()}|${name.trim()}`;

  // Save details to localStorage on successful search
  const saveToLocalStorage = (id: string, policy: string, name: string) => {
    const key = getStorageKey(id, policy, name);
    localStorage.setItem(key, JSON.stringify({ id, policy, name }));
  };

  // Try to auto-fill other fields if a match is found in localStorage
  const tryAutoFill = (changedField: 'id' | 'policy' | 'name', value: string) => {
    // Only auto-fill if the other fields are empty
    let found = false;
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('customerData:')) {
        const data = JSON.parse(localStorage.getItem(key) || '{}');
        if (
          (changedField === 'id' && data.id === value && !policyNumber && !customerName) ||
          (changedField === 'policy' && data.policy === value && !customerId && !customerName) ||
          (changedField === 'name' && data.name === value && !customerId && !policyNumber)
        ) {
          if (changedField !== 'id') setCustomerId(data.id);
          if (changedField !== 'policy') setPolicyNumber(data.policy);
          if (changedField !== 'name') setCustomerName(data.name);
          found = true;
          break;
        }
      }
    }
    return found;
  };

  const handleSearch = async () => {
    console.log('🔍 Starting policy search...');
    setPolicySearchPerformed(true);
    setLoading(true);
    setError(null);

    try {
      const searchRequest: PolicySearchRequest = {
        customer_id: customerId.trim() || undefined,
        policy_number: policyNumber.trim() || undefined,
        customer_name: customerName.trim() || undefined,
      };

      console.log('📤 Search request:', searchRequest);

      const response = await ApiService.searchPolicies(searchRequest);
      console.log('📥 Search response:', response);

      if (response.found && response.customer) {
        console.log('✅ Customer found:', response.customer.name);
        setCurrentCustomer(response.customer);
        saveToLocalStorage(customerId, policyNumber, customerName);
        setError(null);

        // Show success notification
        showNotification(`✅ Customer found successfully: ${response.customer.name}`, true);
      } else {
        console.log('❌ Customer not found:', response.message);
        setCurrentCustomer(null);
        setError(response.message);
        showNotification(`❌ ${response.message}`, false);
      }
    } catch (err) {
      console.error('💥 Error searching policies:', err);
      setError(`Failed to search policies: ${err}`);
      setCurrentCustomer(null);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectPolicy = (policy: PolicyInfo, index: number) => {
    setSelectedPolicyInfo(policy);
    setSelectedPolicyIndex(index);
  };

  // Test function to verify API connection
  const testApiConnection = async () => {
    try {
      console.log('🧪 Testing API connection...');
      setLoading(true);
      const response = await ApiService.searchPolicies({
        customer_id: "CUS-567890",
        policy_number: "POL-12345678",
        customer_name: "John Smith"
      });
      console.log('🧪 Test API response:', response);
      if (response.found) {
        showNotification(`✅ API Test SUCCESS: ${response.message}`, true);
        if (response.customer) {
          setCurrentCustomer(response.customer);
          setPolicySearchPerformed(true);
          setCustomerId("CUS-567890");
          setPolicyNumber("POL-12345678");
          setCustomerName("John Smith");
        }
      } else {
        showNotification(`❌ API Test FAILED: ${response.message}`, false);
      }
    } catch (error) {
      console.error('🧪 Test API error:', error);
      showNotification(`❌ API Test FAILED: ${error}`, false);
    } finally {
      setLoading(false);
    }
  };

  // Test backend health
  const testBackendHealth = async () => {
    try {
      console.log('🏥 Testing backend health...');
      const response = await ApiService.healthCheck();
      console.log('🏥 Health response:', response);
      showNotification(`✅ Backend Health: ONLINE - Status: ${response.status}`, true);
    } catch (error) {
      console.error('🏥 Health check error:', error);
      showNotification(`❌ Backend Health: OFFLINE - ${error}`, false);
    }
  };



  const renderSearchSection = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
      <h2 className="text-xl font-bold text-black mb-6 pb-3 border-b border-gray-200">
        Search Your Policy
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label className="block text-sm font-medium text-black mb-2">Customer ID</label>
          <input
            type="text"
            value={customerId}
            onChange={(e) => {
              const value = e.target.value;
              setCustomerId(value);
              // Try to auto-fill from localStorage
              if (value) tryAutoFill('id', value);
            }}
            placeholder="Enter customer ID"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-black"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-black mb-2">Policy Number</label>
          <input
            type="text"
            value={policyNumber}
            onChange={(e) => {
              setPolicyNumber(e.target.value);
              if (e.target.value) tryAutoFill('policy', e.target.value);
            }}
            placeholder="Enter policy number"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-black"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-black mb-2">Customer Name</label>
          <input
            type="text"
            value={customerName}
            onChange={(e) => {
              setCustomerName(e.target.value);
              if (e.target.value) tryAutoFill('name', e.target.value);
            }}
            placeholder="Enter customer name"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-black"
          />
        </div>
        <div className="flex items-end">
          <button
            onClick={handleSearch}
            disabled={loading}
            className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Search className="w-4 h-4" />
            )}
            {loading ? 'Searching...' : 'Search'}
          </button>
        </div>
      </div>
    </div>
  );

  const renderAvailablePolicies = () => {
    if (!currentCustomer) return null;
    const availablePolicies = currentCustomer.policies || [];
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-black">Your Policies</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Policy Number
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sum Assurance
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                 Annual Premium
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  View
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {availablePolicies.map((policy: PolicyInfo, index: number) => {
                const statusClass = policy.status === "Active"
                  ? "inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"
                  : "inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800";
                return (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {policy.policy_number}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={statusClass}>{policy.status}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {policy.policy_details.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600 text-left">
                      {policy.policy_details.coverage.includes("$") && !policy.policy_details.coverage.includes("%")
                        ? `$${policy.policy_details.coverage.replace(/\s*\$/g, "")}`
                        : policy.policy_details.coverage}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {policy.policy_details.premium}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={() => handleSelectPolicy(policy, index)}
                        className="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700 transition-colors"
                      >
                        Select
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderSelectedPolicyDetails = () => {
    if (!selectedPolicyInfo || !currentCustomer) return null;
    return (
      <div className="space-y-8">
        <h2 className="text-xl font-semibold text-gray-900 pb-3 border-b border-gray-200">
          Selected Policy Details
        </h2>
        {/* Customer and Policy Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Customer Information */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-2 mb-4">
              <User className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-bold text-black">Customer Information</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Full Name</span>
                <span className="text-sm text-gray-900">{currentCustomer.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Date of Birth</span>
                <span className="text-sm text-gray-900">{currentCustomer.dob}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Customer ID</span>
                <span className="text-sm text-gray-900">{currentCustomer.customer_id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Email</span>
                <span className="text-sm text-gray-900">{currentCustomer.email}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Phone</span>
                <span className="text-sm text-gray-900">{currentCustomer.phone}</span>
              </div>
            </div>
          </div>
          {/* Coverage Information */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-2 mb-4">
              <Shield className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-bold text-black">Coverage Information</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Policy Type</span>
                <span className="text-sm text-gray-900">{selectedPolicyInfo.policy_details.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Face Amount</span>
                <span className="text-sm text-gray-900">{selectedPolicyInfo.policy_details.coverage}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Premium</span>
                <span className="text-sm text-gray-900">{selectedPolicyInfo.policy_details.premium}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Premium Type</span>
                <span className="text-sm text-gray-900">{selectedPolicyInfo.policy_details.premium_type}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Payment Frequency</span>
                <span className="text-sm text-gray-900">{selectedPolicyInfo.policy_details.payment_frequency}</span>
              </div>
            </div>
          </div>
          {/* Policy Details */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-2 mb-4">
              <FileText className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-bold text-black">Policy Details</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Policy Number</span>
                <span className="text-sm text-gray-900">{selectedPolicyInfo.policy_number}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Issue Date</span>
                <span className="text-sm text-gray-900">{selectedPolicyInfo.policy_details.issue_date}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Status</span>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  selectedPolicyInfo.status === 'Active'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {selectedPolicyInfo.status}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Type</span>
                <span className="text-sm text-gray-900">{selectedPolicyInfo.policy_details.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Premium</span>
                <span className="text-sm text-gray-900">{selectedPolicyInfo.policy_details.premium}</span>
              </div>
            </div>
          </div>
          {/* Financial Details */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-2 mb-4">
              <DollarSign className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-bold text-black">Financial Details</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Face Amount</span>
                <span className="text-sm text-gray-900">{selectedPolicyInfo.policy_details.coverage}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Cash Value</span>
                <span className="text-sm text-gray-900">{selectedPolicyInfo.policy_details.cash_value}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Premium</span>
                <span className="text-sm text-gray-900">{selectedPolicyInfo.policy_details.premium}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Payment Frequency</span>
                <span className="text-sm text-gray-900">{selectedPolicyInfo.policy_details.payment_frequency}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Next Due Date</span>
                <span className="text-sm text-gray-900">{selectedPolicyInfo.policy_details.next_due_date}</span>
              </div>
            </div>
          </div>
        </div>
        {/* Payment History and Riders */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Payment History */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center gap-2">
                <CreditCard className="w-5 h-5 text-blue-600" />
                <h3 className="text-lg font-bold text-black">Payment History</h3>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Date</th>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Amount</th>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Status</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {selectedPolicyInfo.payment_history.map((payment, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{payment.date}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{payment.amount}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          payment.status === 'Paid'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {payment.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          {/* Active Riders */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center gap-2">
                <Activity className="w-5 h-5 text-blue-600" />
                <h3 className="text-lg font-bold text-black">Active Riders</h3>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Rider</th>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Coverage</th>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Status</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {selectedPolicyInfo.active_riders.length > 0 ? (
                    selectedPolicyInfo.active_riders.map((rider, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{rider.name}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{rider.coverage}</td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            rider.status === 'Active'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {rider.status}
                          </span>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={3} className="px-6 py-4 text-center text-sm text-gray-500">
                        No active riders
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
        {/* Transaction History */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-bold text-black">Transaction History</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Transaction ID</th>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Date</th>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Type</th>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Amount</th>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Remarks</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {selectedPolicyInfo.transaction_history.length > 0 ? (
                  selectedPolicyInfo.transaction_history.map((transaction, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{transaction.id}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{transaction.date}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{transaction.type}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${transaction.amount}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{transaction.remarks}</td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                      No transaction history available
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    );
  };

  const renderNoResultsFound = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="text-center">
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
          <p className="text-black font-medium">{error || "Customer not found. Please check your details."}</p>
        </div>
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <p className="text-black font-medium mb-2">Sample Test Data:</p>
          <div className="text-black text-sm space-y-1">
            <p>• CUS-567890 | POL-12345678 | John Smith</p>
            <p>• CUS-123456 | POL-87654321 | Jane Doe</p>
            <p>• CUS-111111 | POL-11111111 | Michael Johnson</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderLoadingState = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-black font-medium">Searching for customer policies...</p>
      </div>
    </div>
  );

  const renderIllustrationSection = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
      <h3 className="text-lg font-bold text-black mb-4">Ready to proceed?</h3>
      <p className="text-gray-600 mb-6">Generate detailed policy illustrations and projections</p>
      <button
        className="bg-blue-600 text-white px-8 py-3 rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center gap-2 mx-auto"
        onClick={() => {
          if (selectedPolicyInfo && currentCustomer) {
            // Save selected customer and policy data to context
            setSelectedCustomerData({
              name: currentCustomer.name,
              policyNumber: selectedPolicyInfo.policy_number,
              customerId: currentCustomer.customer_id,
              details: {
                DOB: currentCustomer.dob,
                Email: currentCustomer.email,
                Phone: currentCustomer.phone,
                Address: currentCustomer.address,
                Occupation: currentCustomer.occupation,
                "Annual Income": currentCustomer.annual_income,
                "Customer ID": currentCustomer.customer_id,
                "Policy Number": selectedPolicyInfo.policy_number,
                "Policy Type": selectedPolicyInfo.policy_type,
                Status: selectedPolicyInfo.status,
              }
            });

            setSelectedPolicyData({
              id: selectedPolicyInfo.policy_details.id,
              name: selectedPolicyInfo.policy_details.name,
              description: selectedPolicyInfo.policy_details.description,
              coverage: selectedPolicyInfo.policy_details.coverage,
              premium: selectedPolicyInfo.policy_details.premium,
              features: selectedPolicyInfo.policy_details.features
            });

            setActiveTab('as-is');
          }
        }}
        disabled={!selectedPolicyInfo || !currentCustomer}
      >
        <Calculator className="w-5 h-5" />
        📊 Go to Illustration
      </button>
    </div>
  );

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">

      {/* Backend Connection Status */}
      <div className={`border-2 rounded-lg p-4 mb-4 ${loading ? 'border-blue-500 bg-blue-50' : 'border-gray-300 bg-white'}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span className="text-blue-600 font-bold text-lg">� LOADING - Connecting to Backend...</span>
              </>
            ) : (
              <>
                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm">✓</span>
                </div>
                <span className="text-green-600 font-bold text-lg">✅ Backend Ready</span>
              </>
            )}
          </div>

          {/* Status Indicators */}
          <div className="flex gap-4 text-sm">
            <div className={`px-2 py-1 rounded ${policySearchPerformed ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
              Search: {policySearchPerformed ? 'Done' : 'Pending'}
            </div>
            <div className={`px-2 py-1 rounded ${currentCustomer ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
              Data: {currentCustomer ? 'Loaded' : 'None'}
            </div>
            {error && (
              <div className="px-2 py-1 rounded bg-red-100 text-red-800">
                Error: {error}
              </div>
            )}
          </div>
        </div>

        {/* Test Buttons */}
        <div className="flex gap-2 mt-3">
          <button
            onClick={testBackendHealth}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors font-medium"
          >
            🏥 Test Backend Health
          </button>
          <button
            onClick={testApiConnection}
            disabled={loading}
            className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:bg-green-400 transition-colors font-medium"
          >
            {loading ? '🔄 Testing...' : '🧪 Test API Connection'}
          </button>
        </div>
      </div>

      {/* Loading Overlay */}
      {loading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 text-center shadow-2xl">
            <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-blue-600 mx-auto mb-4"></div>
            <h3 className="text-xl font-bold text-black mb-2">Connecting to Backend</h3>
            <p className="text-gray-600">Please wait while we fetch your policy data...</p>
            <div className="mt-4 text-sm text-gray-500">
              Establishing secure connection...
            </div>
          </div>
        </div>
      )}

      {/* Main Title */}
      <div>
        <h1 className="text-2xl font-bold text-black">Know Your Policy</h1>
        <p className="text-black">Enter customer details to search and select a policy for illustration.</p>
      </div>
      {/* Search Section */}
      {renderSearchSection()}
      {/* Search Results */}
      {policySearchPerformed && (
        <>
          {loading ? (
            renderLoadingState()
          ) : currentCustomer ? (
            <>
              {renderAvailablePolicies()}
              {selectedPolicyInfo && renderSelectedPolicyDetails()}
            </>
          ) : (
            renderNoResultsFound()
          )}
        </>
      )}
      {/* Info Message when no search performed */}
      {!policySearchPerformed && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <p className="text-black font-medium">Enter customer details and click Search to view available policies.</p>
        </div>
      )}
      {/* Illustration Section */}
      {renderIllustrationSection()}
    </div>
  );
};
export default PolicySelection;
