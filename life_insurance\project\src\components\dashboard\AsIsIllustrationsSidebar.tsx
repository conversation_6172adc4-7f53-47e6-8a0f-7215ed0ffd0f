import React, { useState, useEffect } from 'react';
import { Save, Trash2, Eye, Calendar, User, FileText, AlertCircle, Loader2 } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import { AsIsIllustration, Scenario } from '../../types';
import { ApiService } from '../../services/api';
import { useDashboard } from '../../contexts/DashboardContext';

interface AsIsIllustrationsSidebarProps {
  onSelectIllustration?: (illustration: AsIsIllustration) => void;
  onDeleteIllustration?: (illustrationId: string) => void;
  refreshTrigger?: number;
}

// Combined item type for both illustrations and scenarios
interface AsIsItem {
  id: string;
  name: string;
  policyNumber: string;
  customerName: string;
  customerId: string;
  data: any;
  createdAt: Date;
  updatedAt: Date;
  type: 'illustration' | 'scenario';
}

const AsIsIllustrationsSidebar: React.FC<AsIsIllustrationsSidebarProps> = ({
  onSelectIllustration,
  onDeleteIllustration,
  refreshTrigger = 0
}) => {
  const [items, setItems] = useState<AsIsItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedId, setSelectedId] = useState<string | null>(null);

  // Get scenarios from dashboard context
  const { scenarios } = useDashboard();

  const loadItems = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load As-Is illustrations from the dedicated backend
      const illustrations = await ApiService.getAsIsIllustrations();

      // Filter As-Is scenarios from the main scenarios list
      const asIsScenarios = scenarios.filter(scenario => scenario.category === 'as-is');

      // Combine both into a unified list
      const combinedItems: AsIsItem[] = [
        // Convert illustrations to items
        ...illustrations.map(ill => ({
          id: ill.id,
          name: ill.name,
          policyNumber: ill.policyNumber,
          customerName: ill.customerName,
          customerId: ill.customerId,
          data: ill.data,
          createdAt: ill.createdAt,
          updatedAt: ill.updatedAt,
          type: 'illustration' as const
        })),
        // Convert scenarios to items
        ...asIsScenarios.map(scenario => ({
          id: scenario.id,
          name: scenario.name,
          policyNumber: scenario.policyId,
          customerName: scenario.data?.policyData?.customerName || 'Unknown',
          customerId: scenario.data?.policyData?.customerId || 'Unknown',
          data: scenario.data,
          createdAt: scenario.createdAt,
          updatedAt: scenario.updatedAt,
          type: 'scenario' as const
        }))
      ];

      // Sort by creation date (newest first)
      combinedItems.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

      setItems(combinedItems);
    } catch (err) {
      console.error('Error loading As-Is items:', err);
      setError(err instanceof Error ? err.message : 'Failed to load items');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadItems();
  }, [refreshTrigger, scenarios]); // Also reload when scenarios change

  const handleSelectItem = (item: AsIsItem) => {
    setSelectedId(item.id);

    // Convert item back to AsIsIllustration format for the callback
    const illustration: AsIsIllustration = {
      id: item.id,
      userId: 'current-user', // This will be set properly by the backend
      name: item.name,
      policyNumber: item.policyNumber,
      customerName: item.customerName,
      customerId: item.customerId,
      data: item.data,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt
    };

    onSelectIllustration?.(illustration);
  };

  const handleDeleteItem = async (itemId: string, itemType: 'illustration' | 'scenario') => {
    if (!confirm('Are you sure you want to delete this As-Is configuration?')) {
      return;
    }

    try {
      if (itemType === 'illustration') {
        await ApiService.deleteAsIsIllustration(itemId);
      } else {
        // For scenarios, we would need to call the scenario delete API
        // This is handled by the dashboard context
        console.log('Scenario deletion should be handled by the dashboard context');
        return; // Don't allow deletion of scenarios from here for now
      }

      setItems(prev => prev.filter(item => item.id !== itemId));
      onDeleteIllustration?.(itemId);

      if (selectedId === itemId) {
        setSelectedId(null);
      }
    } catch (err) {
      console.error('Error deleting item:', err);
      alert('Failed to delete item. Please try again.');
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  if (loading) {
    return (
      <Card className="h-full">
        <div className="flex items-center justify-center h-32">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600 dark:text-gray-400">Loading illustrations...</span>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="h-full">
        <div className="flex items-center justify-center h-32 text-red-600 dark:text-red-400">
          <AlertCircle className="w-6 h-6 mr-2" />
          <span>{error}</span>
        </div>
        <Button 
          onClick={loadIllustrations}
          variant="outline"
          className="w-full mt-4"
        >
          Retry
        </Button>
      </Card>
    );
  }

  return (
    <Card className="h-full">
      <div className="flex items-center space-x-3 mb-4">
        <FileText className="w-5 h-5 text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Saved As-Is Illustrations
        </h3>
      </div>

      {items.length === 0 ? (
        <div className="text-center py-8">
          <FileText className="w-12 h-12 text-gray-400 mx-auto mb-3" />
          <p className="text-gray-500 dark:text-gray-400 text-sm">
            No As-Is configurations saved yet.
          </p>
          <p className="text-gray-400 dark:text-gray-500 text-xs mt-1">
            Create and save an As-Is configuration to see it here.
          </p>
        </div>
      ) : (
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {items.map((item) => (
            <div
              key={item.id}
              className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                selectedId === item.id
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
              onClick={() => handleSelectItem(item)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                      {item.name}
                    </h4>
                    <span className={`px-1.5 py-0.5 text-xs rounded ${
                      item.type === 'scenario'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                        : 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                    }`}>
                      {item.type === 'scenario' ? 'Scenario' : 'Saved'}
                    </span>
                  </div>

                  <div className="flex items-center space-x-2 mt-1">
                    <User className="w-3 h-3 text-gray-400" />
                    <span className="text-xs text-gray-600 dark:text-gray-400 truncate">
                      {item.customerName}
                    </span>
                  </div>

                  <div className="flex items-center space-x-2 mt-1">
                    <FileText className="w-3 h-3 text-gray-400" />
                    <span className="text-xs text-gray-600 dark:text-gray-400">
                      Policy: {item.policyNumber}
                    </span>
                  </div>

                  <div className="flex items-center space-x-2 mt-1">
                    <Calendar className="w-3 h-3 text-gray-400" />
                    <span className="text-xs text-gray-500 dark:text-gray-500">
                      {formatDate(item.createdAt)}
                    </span>
                  </div>
                </div>

                <div className="flex items-center space-x-1 ml-2">
                  <Button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSelectItem(item);
                    }}
                    variant="outline"
                    size="sm"
                    className="p-1 h-6 w-6"
                    title="Load configuration"
                  >
                    <Eye className="w-3 h-3" />
                  </Button>

                  {item.type === 'illustration' && (
                    <Button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteItem(item.id, item.type);
                      }}
                      variant="outline"
                      size="sm"
                      className="p-1 h-6 w-6 text-red-600 hover:text-red-700 hover:border-red-300"
                      title="Delete saved illustration"
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  )}
                </div>
              </div>

              {/* Quick preview of key data */}
              <div className="mt-2 pt-2 border-t border-gray-100 dark:border-gray-700">
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div>
                    <span className="text-gray-500 dark:text-gray-500">Face Amount:</span>
                    <div className="font-medium text-gray-700 dark:text-gray-300">
                      ${parseInt(item.data?.policyData?.faceAmount || '0').toLocaleString()}
                    </div>
                  </div>
                  <div>
                    <span className="text-gray-500 dark:text-gray-500">Premium:</span>
                    <div className="font-medium text-gray-700 dark:text-gray-300">
                      ${parseInt(item.data?.policyData?.annualPremium || '0').toLocaleString()}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      
      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <Button
          onClick={loadItems}
          variant="outline"
          className="w-full text-sm"
        >
          Refresh List
        </Button>
      </div>
    </Card>
  );
};

export default AsIsIllustrationsSidebar;
