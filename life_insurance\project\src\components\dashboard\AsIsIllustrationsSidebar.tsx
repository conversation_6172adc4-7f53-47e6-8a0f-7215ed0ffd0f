import React, { useState, useEffect } from 'react';
import { Save, Trash2, Eye, Calendar, User, FileText, AlertCircle, Loader2 } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import { AsIsIllustration } from '../../types';
import { ApiService } from '../../services/api';

interface AsIsIllustrationsSidebarProps {
  onSelectIllustration?: (illustration: AsIsIllustration) => void;
  onDeleteIllustration?: (illustrationId: string) => void;
  refreshTrigger?: number;
}

const AsIsIllustrationsSidebar: React.FC<AsIsIllustrationsSidebarProps> = ({
  onSelectIllustration,
  onDeleteIllustration,
  refreshTrigger = 0
}) => {
  const [illustrations, setIllustrations] = useState<AsIsIllustration[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedId, setSelectedId] = useState<string | null>(null);

  const loadIllustrations = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await ApiService.getAsIsIllustrations();
      setIllustrations(data);
    } catch (err) {
      console.error('Error loading As-Is illustrations:', err);
      setError(err instanceof Error ? err.message : 'Failed to load illustrations');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadIllustrations();
  }, [refreshTrigger]);

  const handleSelectIllustration = (illustration: AsIsIllustration) => {
    setSelectedId(illustration.id);
    onSelectIllustration?.(illustration);
  };

  const handleDeleteIllustration = async (illustrationId: string) => {
    if (!confirm('Are you sure you want to delete this As-Is illustration?')) {
      return;
    }

    try {
      await ApiService.deleteAsIsIllustration(illustrationId);
      setIllustrations(prev => prev.filter(ill => ill.id !== illustrationId));
      onDeleteIllustration?.(illustrationId);
      
      if (selectedId === illustrationId) {
        setSelectedId(null);
      }
    } catch (err) {
      console.error('Error deleting illustration:', err);
      alert('Failed to delete illustration. Please try again.');
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  if (loading) {
    return (
      <Card className="h-full">
        <div className="flex items-center justify-center h-32">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600 dark:text-gray-400">Loading illustrations...</span>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="h-full">
        <div className="flex items-center justify-center h-32 text-red-600 dark:text-red-400">
          <AlertCircle className="w-6 h-6 mr-2" />
          <span>{error}</span>
        </div>
        <Button 
          onClick={loadIllustrations}
          variant="outline"
          className="w-full mt-4"
        >
          Retry
        </Button>
      </Card>
    );
  }

  return (
    <Card className="h-full">
      <div className="flex items-center space-x-3 mb-4">
        <FileText className="w-5 h-5 text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Saved As-Is Illustrations
        </h3>
      </div>

      {illustrations.length === 0 ? (
        <div className="text-center py-8">
          <FileText className="w-12 h-12 text-gray-400 mx-auto mb-3" />
          <p className="text-gray-500 dark:text-gray-400 text-sm">
            No As-Is illustrations saved yet.
          </p>
          <p className="text-gray-400 dark:text-gray-500 text-xs mt-1">
            Create and save an illustration to see it here.
          </p>
        </div>
      ) : (
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {illustrations.map((illustration) => (
            <div
              key={illustration.id}
              className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                selectedId === illustration.id
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
              onClick={() => handleSelectIllustration(illustration)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                    {illustration.name}
                  </h4>
                  
                  <div className="flex items-center space-x-2 mt-1">
                    <User className="w-3 h-3 text-gray-400" />
                    <span className="text-xs text-gray-600 dark:text-gray-400 truncate">
                      {illustration.customerName}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-2 mt-1">
                    <FileText className="w-3 h-3 text-gray-400" />
                    <span className="text-xs text-gray-600 dark:text-gray-400">
                      Policy: {illustration.policyNumber}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-2 mt-1">
                    <Calendar className="w-3 h-3 text-gray-400" />
                    <span className="text-xs text-gray-500 dark:text-gray-500">
                      {formatDate(illustration.createdAt)}
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center space-x-1 ml-2">
                  <Button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSelectIllustration(illustration);
                    }}
                    variant="outline"
                    size="sm"
                    className="p-1 h-6 w-6"
                    title="View illustration"
                  >
                    <Eye className="w-3 h-3" />
                  </Button>
                  
                  <Button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteIllustration(illustration.id);
                    }}
                    variant="outline"
                    size="sm"
                    className="p-1 h-6 w-6 text-red-600 hover:text-red-700 hover:border-red-300"
                    title="Delete illustration"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              </div>
              
              {/* Quick preview of key data */}
              <div className="mt-2 pt-2 border-t border-gray-100 dark:border-gray-700">
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div>
                    <span className="text-gray-500 dark:text-gray-500">Face Amount:</span>
                    <div className="font-medium text-gray-700 dark:text-gray-300">
                      ${parseInt(illustration.data.policyData.faceAmount || '0').toLocaleString()}
                    </div>
                  </div>
                  <div>
                    <span className="text-gray-500 dark:text-gray-500">Premium:</span>
                    <div className="font-medium text-gray-700 dark:text-gray-300">
                      ${parseInt(illustration.data.policyData.annualPremium || '0').toLocaleString()}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      
      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <Button
          onClick={loadIllustrations}
          variant="outline"
          className="w-full text-sm"
        >
          Refresh List
        </Button>
      </div>
    </Card>
  );
};

export default AsIsIllustrationsSidebar;
