import React, { useState } from 'react';
import { Download, Filter, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, TrendingUp, Mail, MessageCircle, Share2, User, FileText, DollarSign, Calendar } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import Select from '../common/Select';
import { useDashboard } from '../../contexts/DashboardContext';
import {
  <PERSON><PERSON>hart as RechartsLineChart,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>Bar<PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON>rts<PERSON>ie<PERSON><PERSON>,
  AreaChart,
  ComposedChart,
  Line,
  Bar,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell,
  Pie
} from 'recharts';

const AnalysisReports: React.FC = () => {
  const {
    scenarios,
    selectedScenarios,
    selectedCustomerData,
    selectedPolicyData,
    currentCustomer
  } = useDashboard();

  const [selectedScenario, setSelectedScenario] = useState('');
  const [timeRange, setTimeRange] = useState('5-years');
  const [showGraphOptions, setShowGraphOptions] = useState(false);
  const [selectedGraphType, setSelectedGraphType] = useState('line');
  const [showExportOptions, setShowExportOptions] = useState(false);
  const [analysisGenerated, setAnalysisGenerated] = useState(false);
  const [showGraphs, setShowGraphs] = useState(false);

  // Mock data for demonstration
  const mockScenarios = [
    { id: '1', name: 'Whole Life Premium Analysis' },
    { id: '2', name: 'Face Amount Increase Scenario' },
    { id: '3', name: 'Loan Repayment Strategy' },
  ];

  const availableScenarios = scenarios.length > 0 ? scenarios : mockScenarios;
  const scenarioOptions = availableScenarios
    .filter(scenario => selectedScenarios.includes(scenario.id))
    .map(scenario => ({ value: scenario.id, label: scenario.name }));

  // Auto-select first scenario if available and none selected
  React.useEffect(() => {
    if (scenarioOptions.length > 0 && !selectedScenario) {
      setSelectedScenario(scenarioOptions[0].value);
      setAnalysisGenerated(true);
    }
  }, [scenarioOptions, selectedScenario]);

  const timeRanges = [
    { value: '1-year', label: '1 Year' },
    { value: '5-years', label: '5 Years' },
    { value: '10-years', label: '10 Years' },
    { value: '20-years', label: '20 Years' },
    { value: 'lifetime', label: 'Lifetime' },
  ];

  const graphTypes = [
    { value: 'pie', label: 'Pie Chart', icon: PieChart },
    { value: 'bar', label: 'Bar Chart', icon: BarChart3 },
    { value: 'line', label: 'Line Chart', icon: LineChart },
  ];

  // Enhanced mock data for comprehensive analysis
  const analysisData = [
    { year: 2024, age: 31, premium: 25000, cashValue: 5000, deathBenefit: 500000, surrender: 0, loanBalance: 0, dividends: 1250, netReturn: 2.5, status: 'Active', totalPremiums: 25000 },
    { year: 2025, age: 32, premium: 25000, cashValue: 11500, deathBenefit: 506250, surrender: 0, loanBalance: 0, dividends: 1312, netReturn: 3.2, status: 'Active', totalPremiums: 50000 },
    { year: 2026, age: 33, premium: 25000, cashValue: 18200, deathBenefit: 512656, surrender: 0, loanBalance: 0, dividends: 1377, netReturn: 3.8, status: 'Active', totalPremiums: 75000 },
    { year: 2027, age: 34, premium: 25000, cashValue: 25100, deathBenefit: 519224, surrender: 0, loanBalance: 0, dividends: 1446, netReturn: 4.1, status: 'Active', totalPremiums: 100000 },
    { year: 2028, age: 35, premium: 25000, cashValue: 32200, deathBenefit: 525960, surrender: 0, loanBalance: 0, dividends: 1518, netReturn: 4.5, status: 'Active', totalPremiums: 125000 },
    { year: 2029, age: 36, premium: 25000, cashValue: 39500, deathBenefit: 532868, surrender: 0, loanBalance: 0, dividends: 1594, netReturn: 4.8, status: 'Active', totalPremiums: 150000 },
    { year: 2030, age: 37, premium: 25000, cashValue: 47000, deathBenefit: 539953, surrender: 0, loanBalance: 0, dividends: 1673, netReturn: 5.0, status: 'Active', totalPremiums: 175000 },
    { year: 2031, age: 38, premium: 25000, cashValue: 54700, deathBenefit: 547220, surrender: 0, loanBalance: 0, dividends: 1756, netReturn: 5.2, status: 'Active', totalPremiums: 200000 },
    { year: 2032, age: 39, premium: 25000, cashValue: 62600, deathBenefit: 554674, surrender: 0, loanBalance: 0, dividends: 1844, netReturn: 5.4, status: 'Active', totalPremiums: 225000 },
    { year: 2033, age: 40, premium: 25000, cashValue: 70700, deathBenefit: 562321, surrender: 0, loanBalance: 0, dividends: 1935, netReturn: 5.6, status: 'Active', totalPremiums: 250000 },
    { year: 2034, age: 41, premium: 25000, cashValue: 79000, deathBenefit: 570175, surrender: 0, loanBalance: 0, dividends: 2029, netReturn: 5.8, status: 'Active', totalPremiums: 275000 },
    { year: 2035, age: 42, premium: 25000, cashValue: 87500, deathBenefit: 578242, surrender: 0, loanBalance: 0, dividends: 2127, netReturn: 6.0, status: 'Active', totalPremiums: 300000 },
    { year: 2040, age: 47, premium: 25000, cashValue: 135000, deathBenefit: 625000, surrender: 0, loanBalance: 0, dividends: 2850, netReturn: 6.8, status: 'Active', totalPremiums: 425000 },
    { year: 2045, age: 52, premium: 25000, cashValue: 195000, deathBenefit: 680000, surrender: 0, loanBalance: 0, dividends: 3420, netReturn: 7.2, status: 'Active', totalPremiums: 550000 },
    { year: 2050, age: 57, premium: 25000, cashValue: 270000, deathBenefit: 745000, surrender: 0, loanBalance: 0, dividends: 4100, netReturn: 7.5, status: 'Active', totalPremiums: 675000 },
    { year: 2055, age: 62, premium: 25000, cashValue: 360000, deathBenefit: 820000, surrender: 0, loanBalance: 0, dividends: 4900, netReturn: 7.8, status: 'Active', totalPremiums: 800000 },
    { year: 2060, age: 67, premium: 0, cashValue: 465000, deathBenefit: 905000, surrender: 0, loanBalance: 0, dividends: 5850, netReturn: 8.0, status: 'Paid-Up', totalPremiums: 800000 },
    { year: 2065, age: 72, premium: 0, cashValue: 580000, deathBenefit: 1000000, surrender: 0, loanBalance: 0, dividends: 6950, netReturn: 8.2, status: 'Paid-Up', totalPremiums: 800000 },
    { year: 2070, age: 77, premium: 0, cashValue: 710000, deathBenefit: 1105000, surrender: 0, loanBalance: 0, dividends: 8200, netReturn: 8.4, status: 'Paid-Up', totalPremiums: 800000 },
    { year: 2075, age: 82, premium: 0, cashValue: 855000, deathBenefit: 1220000, surrender: 0, loanBalance: 0, dividends: 9650, netReturn: 8.5, status: 'Paid-Up', totalPremiums: 800000 },
  ];

  // Chart color scheme
  const colors = {
    primary: '#3B82F6',
    secondary: '#10B981',
    accent: '#F59E0B',
    danger: '#EF4444',
    purple: '#8B5CF6',
    teal: '#14B8A6'
  };

  // Pie chart data for policy breakdown
  const policyBreakdownData = [
    { name: 'Cash Value', value: analysisData[analysisData.length - 1].cashValue, color: colors.primary },
    { name: 'Death Benefit', value: analysisData[analysisData.length - 1].deathBenefit - analysisData[analysisData.length - 1].cashValue, color: colors.secondary },
    { name: 'Total Premiums', value: analysisData[analysisData.length - 1].totalPremiums, color: colors.accent },
    { name: 'Total Dividends', value: analysisData.reduce((sum, item) => sum + item.dividends, 0), color: colors.purple }
  ];

  const handleGetIllustration = () => {
    setAnalysisGenerated(true);
    setShowGraphs(true);
    setSelectedGraphType('line');
    console.log('Generating illustration for scenario:', selectedScenario);
  };

  const handleGetGraphData = () => {
    setShowGraphOptions(true);
  };

  const handleGraphTypeSelect = (type: string) => {
    setSelectedGraphType(type);
    setShowGraphOptions(false);
    setShowGraphs(true);
    console.log('Generating', type, 'chart for scenario:', selectedScenario);
  };

  // Format currency for display
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 dark:text-gray-100">{`Year: ${label}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {`${entry.dataKey}: ${formatCurrency(entry.value)}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  const handleExportData = () => {
    setShowExportOptions(true);
  };

  const handleDownload = () => {
    // Create CSV data
    const csvData = analysisData.map(row => Object.values(row).join(',')).join('\n');
    const headers = 'Year,Premium,Cash Value,Death Benefit,Surrender Value,Loan Balance,Dividends,Net Return %,Status\n';
    const csv = headers + csvData;
    
    // Create and download file
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'analysis-report.csv';
    a.click();
    window.URL.revokeObjectURL(url);
    setShowExportOptions(false);
  };

  const handleEmailShare = () => {
    const subject = encodeURIComponent('Insurance Analysis Report');
    const body = encodeURIComponent('Please find the attached insurance analysis report.');
    window.open(`mailto:?subject=${subject}&body=${body}`);
    setShowExportOptions(false);
  };

  const handleWhatsAppShare = () => {
    const message = encodeURIComponent('Insurance Analysis Report - Check out this detailed policy analysis.');
    window.open(`https://wa.me/?text=${message}`);
    setShowExportOptions(false);
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Analysis Reports</h1>
        <p className="text-gray-600 dark:text-gray-400">Comprehensive analysis of your selected insurance policy scenarios.</p>
      </div>

      {/* Policy Holder Information */}
      {(selectedCustomerData || currentCustomer) && (
        <Card>
          <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
              <User className="w-5 h-5 text-blue-600" />
              <span>Policy Holder Information</span>
            </h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Customer Info */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <User className="w-4 h-4 text-gray-500" />
                <h4 className="font-medium text-gray-900 dark:text-gray-100">Customer</h4>
              </div>
              <div className="space-y-2">
                <div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Name:</span>
                  <p className="font-medium text-gray-900 dark:text-gray-100">
                    {selectedCustomerData?.name || currentCustomer?.name || 'N/A'}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Customer ID:</span>
                  <p className="font-medium text-blue-600 dark:text-blue-400">
                    {selectedCustomerData?.customerId || currentCustomer?.customer_id || 'N/A'}
                  </p>
                </div>
              </div>
            </div>

            {/* Policy Info */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <FileText className="w-4 h-4 text-gray-500" />
                <h4 className="font-medium text-gray-900 dark:text-gray-100">Policy</h4>
              </div>
              <div className="space-y-2">
                <div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Policy Number:</span>
                  <p className="font-medium text-blue-600 dark:text-blue-400">
                    {selectedCustomerData?.policyNumber || currentCustomer?.policies?.[0]?.policy_number || 'N/A'}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Type:</span>
                  <p className="font-medium text-gray-900 dark:text-gray-100">
                    {currentCustomer?.policies?.[0]?.policy_type || 'Whole Life Insurance'}
                  </p>
                </div>
              </div>
            </div>

            {/* Financial Info */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <DollarSign className="w-4 h-4 text-gray-500" />
                <h4 className="font-medium text-gray-900 dark:text-gray-100">Financial</h4>
              </div>
              <div className="space-y-2">
                <div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Coverage:</span>
                  <p className="font-medium text-green-600 dark:text-green-400">
                    {formatCurrency(parseInt(String(currentCustomer?.policies?.[0]?.policy_details?.coverage || '500000').replace(/[^0-9]/g, '')))}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Annual Premium:</span>
                  <p className="font-medium text-blue-600 dark:text-blue-400">
                    {formatCurrency(parseInt(String(currentCustomer?.policies?.[0]?.policy_details?.premium || '25000').replace(/[^0-9]/g, '')))}
                  </p>
                </div>
              </div>
            </div>

            {/* Analysis Info */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4 text-gray-500" />
                <h4 className="font-medium text-gray-900 dark:text-gray-100">Analysis</h4>
              </div>
              <div className="space-y-2">
                <div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Scenarios Selected:</span>
                  <p className="font-medium text-purple-600 dark:text-purple-400">
                    {selectedScenarios.length}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Time Range:</span>
                  <p className="font-medium text-gray-900 dark:text-gray-100">
                    {timeRanges.find(r => r.value === timeRange)?.label || '5 Years'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Get Illustration Button */}
          <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Generate comprehensive graphical analysis for the selected scenarios
              </div>
              <Button
                onClick={handleGetIllustration}
                variant="primary"
                size="lg"
                className="flex items-center space-x-2"
                disabled={selectedScenarios.length === 0}
              >
                <TrendingUp className="w-5 h-5" />
                <span>Get Illustration</span>
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* No Scenarios Selected Warning */}
      {selectedScenarios.length === 0 && (
        <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-800 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 dark:text-yellow-400 text-sm">⚠</span>
            </div>
            <div>
              <h3 className="font-medium text-yellow-800 dark:text-yellow-200">No Scenarios Selected</h3>
              <p className="text-yellow-700 dark:text-yellow-300 text-sm">Please go to Selected Scenarios tab and choose scenarios for analysis.</p>
            </div>
          </div>
        </Card>
      )}

      {/* Report Controls */}
      {selectedScenarios.length > 0 && (
        <Card>
        <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
          <div className="flex gap-4 items-center">
            <Select
              label="Selected Scenario"
              value={selectedScenario}
              onChange={(e) => setSelectedScenario(e.target.value)}
              options={scenarioOptions.length > 0 ? scenarioOptions : [{ value: '', label: 'No scenarios selected' }]}
            />
            <Select
              label="Time Range"
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              options={timeRanges}
            />
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline" className="flex items-center space-x-2">
              <Filter className="w-4 h-4" />
              <span>Filter</span>
            </Button>
          </div>
        </div>
        </Card>
      )}

      {selectedScenario && analysisGenerated && (
        <>
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-3">
                <TrendingUp className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">IRR</h3>
              <p className="text-2xl font-bold text-blue-600">4.8%</p>
              <p className="text-sm text-gray-500">Internal Rate of Return</p>
            </Card>
            <Card className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-3">
                <BarChart3 className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Cash Value</h3>
              <p className="text-2xl font-bold text-green-600">$70,700</p>
              <p className="text-sm text-gray-500">Year 10 Projection</p>
            </Card>
            <Card className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mx-auto mb-3">
                <LineChart className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Death Benefit</h3>
              <p className="text-2xl font-bold text-purple-600">$562,321</p>
              <p className="text-sm text-gray-500">Year 10 Projection</p>
            </Card>
            <Card className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-orange-100 rounded-lg mx-auto mb-3">
                <PieChart className="w-6 h-6 text-orange-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Risk Score</h3>
              <p className="text-2xl font-bold text-orange-600">Low</p>
              <p className="text-sm text-gray-500">Risk Assessment</p>
            </Card>
          </div>

          {/* Graphical Charts Section */}
          {showGraphs && (
            <div className="space-y-6">
              {/* Chart Type Selector */}
              <Card>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Graphical Analysis</h3>
                  <div className="flex items-center space-x-2">
                    {graphTypes.map((type) => (
                      <Button
                        key={type.value}
                        onClick={() => setSelectedGraphType(type.value)}
                        variant={selectedGraphType === type.value ? 'primary' : 'outline'}
                        size="sm"
                        className="flex items-center space-x-2"
                      >
                        <type.icon className="w-4 h-4" />
                        <span>{type.label}</span>
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Line Chart - Cash Value & Death Benefit Growth */}
                {selectedGraphType === 'line' && (
                  <div className="space-y-6">
                    <div>
                      <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                        Policy Growth Over Time
                      </h4>
                      <ResponsiveContainer width="100%" height={400}>
                        <RechartsLineChart data={analysisData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="year" />
                          <YAxis tickFormatter={(value) => formatCurrency(value)} />
                          <Tooltip content={<CustomTooltip />} />
                          <Legend />
                          <Line
                            type="monotone"
                            dataKey="cashValue"
                            stroke={colors.primary}
                            strokeWidth={3}
                            name="Cash Value"
                          />
                          <Line
                            type="monotone"
                            dataKey="deathBenefit"
                            stroke={colors.secondary}
                            strokeWidth={3}
                            name="Death Benefit"
                          />
                          <Line
                            type="monotone"
                            dataKey="totalPremiums"
                            stroke={colors.accent}
                            strokeWidth={2}
                            strokeDasharray="5 5"
                            name="Total Premiums Paid"
                          />
                        </RechartsLineChart>
                      </ResponsiveContainer>
                    </div>

                    <div>
                      <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                        Annual Returns & Dividends
                      </h4>
                      <ResponsiveContainer width="100%" height={300}>
                        <ComposedChart data={analysisData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="year" />
                          <YAxis yAxisId="left" tickFormatter={(value) => formatCurrency(value)} />
                          <YAxis yAxisId="right" orientation="right" tickFormatter={(value) => `${value}%`} />
                          <Tooltip content={<CustomTooltip />} />
                          <Legend />
                          <Bar yAxisId="left" dataKey="dividends" fill={colors.purple} name="Annual Dividends" />
                          <Line yAxisId="right" type="monotone" dataKey="netReturn" stroke={colors.danger} strokeWidth={2} name="Net Return %" />
                        </ComposedChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                )}

                {/* Bar Chart - Annual Comparison */}
                {selectedGraphType === 'bar' && (
                  <div>
                    <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                      Annual Financial Comparison
                    </h4>
                    <ResponsiveContainer width="100%" height={400}>
                      <RechartsBarChart data={analysisData.slice(0, 10)}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="year" />
                        <YAxis tickFormatter={(value) => formatCurrency(value)} />
                        <Tooltip content={<CustomTooltip />} />
                        <Legend />
                        <Bar dataKey="premium" fill={colors.accent} name="Annual Premium" />
                        <Bar dataKey="cashValue" fill={colors.primary} name="Cash Value" />
                        <Bar dataKey="dividends" fill={colors.purple} name="Dividends" />
                      </RechartsBarChart>
                    </ResponsiveContainer>
                  </div>
                )}

                {/* Pie Chart - Policy Breakdown */}
                {selectedGraphType === 'pie' && (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                        Policy Value Breakdown
                      </h4>
                      <ResponsiveContainer width="100%" height={350}>
                        <RechartsPieChart>
                          <Pie
                            data={policyBreakdownData}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                            outerRadius={120}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            {policyBreakdownData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                        </RechartsPieChart>
                      </ResponsiveContainer>
                    </div>

                    <div>
                      <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                        Policy Performance Summary
                      </h4>
                      <div className="space-y-4">
                        {policyBreakdownData.map((item, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <div className="flex items-center space-x-3">
                              <div
                                className="w-4 h-4 rounded-full"
                                style={{ backgroundColor: item.color }}
                              ></div>
                              <span className="font-medium text-gray-900 dark:text-gray-100">{item.name}</span>
                            </div>
                            <span className="font-bold text-gray-900 dark:text-gray-100">
                              {formatCurrency(item.value)}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </Card>

              {/* Additional Analysis Charts */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Cash Value Growth Area Chart */}
                <Card>
                  <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                    Cash Value Accumulation
                  </h4>
                  <ResponsiveContainer width="100%" height={250}>
                    <AreaChart data={analysisData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="year" />
                      <YAxis tickFormatter={(value) => formatCurrency(value)} />
                      <Tooltip content={<CustomTooltip />} />
                      <Area
                        type="monotone"
                        dataKey="cashValue"
                        stroke={colors.primary}
                        fill={colors.primary}
                        fillOpacity={0.3}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </Card>

                {/* Premium vs Benefits */}
                <Card>
                  <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                    Premium vs Benefits Ratio
                  </h4>
                  <ResponsiveContainer width="100%" height={250}>
                    <RechartsLineChart data={analysisData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="year" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="netReturn"
                        stroke={colors.secondary}
                        strokeWidth={3}
                        name="Net Return %"
                      />
                    </RechartsLineChart>
                  </ResponsiveContainer>
                </Card>
              </div>
            </div>
          )}

          {/* Data Table */}
          <Card>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Detailed Analysis Data</h3>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-200 px-4 py-2 text-left font-medium text-gray-900">Year</th>
                    <th className="border border-gray-200 px-4 py-2 text-left font-medium text-gray-900">Premium</th>
                    <th className="border border-gray-200 px-4 py-2 text-left font-medium text-gray-900">Cash Value</th>
                    <th className="border border-gray-200 px-4 py-2 text-left font-medium text-gray-900">Death Benefit</th>
                    <th className="border border-gray-200 px-4 py-2 text-left font-medium text-gray-900">Surrender Value</th>
                    <th className="border border-gray-200 px-4 py-2 text-left font-medium text-gray-900">Loan Balance</th>
                    <th className="border border-gray-200 px-4 py-2 text-left font-medium text-gray-900">Dividends</th>
                    <th className="border border-gray-200 px-4 py-2 text-left font-medium text-gray-900">Net Return %</th>
                    <th className="border border-gray-200 px-4 py-2 text-left font-medium text-gray-900">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {analysisData.map((row, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="border border-gray-200 px-4 py-2">{row.year}</td>
                      <td className="border border-gray-200 px-4 py-2">${row.premium.toLocaleString()}</td>
                      <td className="border border-gray-200 px-4 py-2">${row.cashValue.toLocaleString()}</td>
                      <td className="border border-gray-200 px-4 py-2">${row.deathBenefit.toLocaleString()}</td>
                      <td className="border border-gray-200 px-4 py-2">${row.surrender.toLocaleString()}</td>
                      <td className="border border-gray-200 px-4 py-2">${row.loanBalance.toLocaleString()}</td>
                      <td className="border border-gray-200 px-4 py-2">${row.dividends.toLocaleString()}</td>
                      <td className="border border-gray-200 px-4 py-2">{row.netReturn}%</td>
                      <td className="border border-gray-200 px-4 py-2">
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs font-medium">
                          {row.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-center space-x-4">
            <Button
              onClick={handleExportData}
              className="flex items-center space-x-2"
              variant="outline"
            >
              <Download className="w-4 h-4" />
              <span>Export Data</span>
            </Button>
            <Button
              onClick={handleGetGraphData}
              className="flex items-center space-x-2"
              variant="secondary"
            >
              <BarChart3 className="w-4 h-4" />
              <span>Get Graph Data</span>
            </Button>
          </div>

          {/* Graph Type Selection Modal */}
          {showGraphOptions && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <Card className="w-full max-w-md">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Select Graph Type</h3>
                <div className="space-y-3">
                  {graphTypes.map((type) => {
                    const Icon = type.icon;
                    return (
                      <button
                        key={type.value}
                        onClick={() => handleGraphTypeSelect(type.value)}
                        className="w-full flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        <Icon className="w-5 h-5 text-blue-600" />
                        <span className="font-medium">{type.label}</span>
                      </button>
                    );
                  })}
                </div>
                <div className="mt-4 flex justify-end">
                  <Button
                    variant="outline"
                    onClick={() => setShowGraphOptions(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </Card>
            </div>
          )}

          {/* Export Options Modal */}
          {showExportOptions && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <Card className="w-full max-w-md">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Export Analysis Data</h3>
                <div className="space-y-3">
                  <button
                    onClick={handleDownload}
                    className="w-full flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <Download className="w-5 h-5 text-blue-600" />
                    <span className="font-medium text-gray-900 dark:text-gray-100">Download CSV</span>
                  </button>
                  <button
                    onClick={handleEmailShare}
                    className="w-full flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <Mail className="w-5 h-5 text-green-600" />
                    <span className="font-medium text-gray-900 dark:text-gray-100">Send via Email</span>
                  </button>
                  <button
                    onClick={handleWhatsAppShare}
                    className="w-full flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <MessageCircle className="w-5 h-5 text-green-600" />
                    <span className="font-medium text-gray-900 dark:text-gray-100">Share on WhatsApp</span>
                  </button>
                </div>
                <div className="mt-4 flex justify-end">
                  <Button
                    variant="outline"
                    onClick={() => setShowExportOptions(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </Card>
            </div>
          )}

          {/* Selected Graph Display */}
          {selectedGraphType && (
            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {graphTypes.find(g => g.value === selectedGraphType)?.label} Visualization
              </h3>
              <div className="h-64 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  {React.createElement(graphTypes.find(g => g.value === selectedGraphType)?.icon || BarChart3, {
                    className: "w-16 h-16 text-blue-400 mx-auto mb-4"
                  })}
                  <p className="text-gray-600">
                    {graphTypes.find(g => g.value === selectedGraphType)?.label} would be displayed here
                  </p>
                  <p className="text-sm text-gray-500 mt-1">Interactive chart showing policy performance data</p>
                </div>
              </div>
            </Card>
          )}
        </>
      )}

      {selectedScenarios.length > 0 && !selectedScenario && (
        <Card>
          <div className="text-center py-8">
            <BarChart3 className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">Please select a scenario from your saved scenarios to view analysis.</p>
            <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">Choose a scenario from the dropdown above.</p>
          </div>
        </Card>
      )}

      {selectedScenarios.length === 0 && (
        <Card>
          <div className="text-center py-12">
            <BarChart3 className="w-20 h-20 text-gray-300 dark:text-gray-600 mx-auto mb-6" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No Scenarios Available</h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">You need to select scenarios before generating analysis reports.</p>
            <div className="space-y-2">
              <p className="text-sm text-gray-400 dark:text-gray-500">1. Go to Selected Scenarios tab</p>
              <p className="text-sm text-gray-400 dark:text-gray-500">2. Choose scenarios to analyze</p>
              <p className="text-sm text-gray-400 dark:text-gray-500">3. Click "Get Illustration" button</p>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default AnalysisReports;