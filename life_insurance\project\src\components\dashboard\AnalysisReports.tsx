import React, { useState, useEffect } from 'react';
import Card from '../common/Card';
import Button from '../common/Button';
import { useDashboard } from '../../contexts/DashboardContext';
import { ApiService } from '../../services/api';
import {
  BarChart3,
  User,
  AlertTriangle,
  Download,
  FileText,
  Mail,
  MessageCircle,
  Printer,
  Share2,
  TrendingUp,
  PieChart,
  Table,
  Target,
  DollarSign,
  Calendar,
  ChevronDown,
  Wifi,
  WifiOff,
  CheckCircle,
  XCircle
} from 'lucide-react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

const AnalysisReports: React.FC = () => {
  const { selectedScenarios, currentCustomer } = useDashboard();
  const [isLoading, setIsLoading] = useState(false);
  const [showReports, setShowReports] = useState(false);
  const [showExportMenu, setShowExportMenu] = useState(false);
  const [backendStatus, setBackendStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');

  // Check backend connection on component mount
  useEffect(() => {
    checkBackendConnection();
  }, []);

  const checkBackendConnection = async () => {
    try {
      setBackendStatus('checking');
      await ApiService.healthCheck();
      setBackendStatus('connected');
    } catch (error) {
      console.error('Backend connection failed:', error);
      setBackendStatus('disconnected');
    }
  };

  const handleGetIllustration = async () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      setShowReports(true);
    }, 2000);
  };

  // Sample data for charts
  const policyPerformanceData = [
    { year: '2024', currentValue: 100000, projectedValue: 105000, premiumPaid: 12000 },
    { year: '2025', currentValue: 112000, projectedValue: 118000, premiumPaid: 24000 },
    { year: '2026', currentValue: 125000, projectedValue: 132000, premiumPaid: 36000 },
    { year: '2027', currentValue: 139000, projectedValue: 147000, premiumPaid: 48000 },
    { year: '2028', currentValue: 154000, projectedValue: 163000, premiumPaid: 60000 },
    { year: '2029', currentValue: 170000, projectedValue: 180000, premiumPaid: 72000 },
    { year: '2030', currentValue: 187000, projectedValue: 198000, premiumPaid: 84000 },
  ];

  const scenarioComparisonData = [
    { scenario: 'Conservative', roi: 4.2, riskLevel: 'Low', projectedValue: 180000 },
    { scenario: 'Moderate', roi: 6.8, riskLevel: 'Medium', projectedValue: 220000 },
    { scenario: 'Aggressive', roi: 9.1, riskLevel: 'High', projectedValue: 280000 },
  ];

  // Policy breakdown data for pie chart
  const policyBreakdownData = [
    { name: 'Death Benefit', value: 500000, color: '#3B82F6', percentage: 62.5 },
    { name: 'Cash Value', value: 187000, color: '#10B981', percentage: 23.4 },
    { name: 'Premium Paid', value: 84000, color: '#F59E0B', percentage: 10.5 },
    { name: 'Dividends', value: 29000, color: '#8B5CF6', percentage: 3.6 },
  ];

  const monthlyPremiumData = [
    { month: 'Jan', premium: 1000, claims: 200 },
    { month: 'Feb', premium: 1000, claims: 150 },
    { month: 'Mar', premium: 1000, claims: 300 },
    { month: 'Apr', premium: 1000, claims: 180 },
    { month: 'May', premium: 1000, claims: 220 },
    { month: 'Jun', premium: 1000, claims: 160 },
  ];

  const handleExport = async (type: string) => {
    setShowExportMenu(false);

    // Show success notification
    const showNotification = (message: string, isSuccess: boolean = true) => {
      // Create a custom notification instead of browser alert
      const notification = document.createElement('div');
      notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
        isSuccess ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
      } transition-all duration-300`;
      notification.innerHTML = `
        <div class="flex items-center space-x-2">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
          <span>${message}</span>
        </div>
      `;
      document.body.appendChild(notification);

      // Remove notification after 3 seconds
      setTimeout(() => {
        notification.remove();
      }, 3000);
    };

    try {
      // Check backend connection before export
      if (backendStatus === 'disconnected') {
        showNotification('Backend connection required for export functionality. Please check connection.', false);
        return;
      }

      switch (type) {
        case 'pdf':
          // Create PDF export and save to backend
          const pdfContent = generateReportContent();
          downloadFile(pdfContent, `analysis-report-${currentCustomer?.name || 'customer'}-${new Date().toISOString().split('T')[0]}.pdf`, 'application/pdf');

          // Save report metadata to backend
          if (backendStatus === 'connected') {
            try {
              // This would be a new API endpoint for saving report metadata
              console.log('Saving report metadata to backend...');
              showNotification('✅ PDF report downloaded and saved to backend successfully!');
            } catch (backendError) {
              showNotification('PDF downloaded locally. Backend save failed.');
            }
          } else {
            showNotification('PDF report downloaded successfully!');
          }
          break;

        case 'csv':
          // Create CSV export
          const csvContent = generateCSVContent();
          downloadFile(csvContent, `analysis-data-${currentCustomer?.name || 'customer'}-${new Date().toISOString().split('T')[0]}.csv`, 'text/csv');
          showNotification('✅ CSV data exported successfully!');
          break;

        case 'email':
          // Open email client with comprehensive report data
          const emailSubject = `📊 Insurance Analysis Report - ${currentCustomer?.name}`;
          const emailBody = `Dear Colleague,

Please find the comprehensive insurance analysis report details below:

📋 CUSTOMER INFORMATION
• Name: ${currentCustomer?.name}
• Email: ${currentCustomer?.email}
• Customer ID: ${currentCustomer?.customer_id}
• Phone: ${currentCustomer?.phone}

📊 ANALYSIS SUMMARY
• Report Date: ${new Date().toLocaleDateString()}
• Scenarios Analyzed: ${selectedScenarios.length}
• Analysis Type: Comprehensive Policy Review

💰 KEY METRICS
• Current Policy Value: $187,000
• Total Premium Paid: $84,000
• Projected ROI: 6.8%
• Net Gain: $103,000

This report contains detailed analysis of the selected insurance policy scenarios including performance charts, scenario comparisons, and recommendations.

Best regards,
Insurance Analysis System`;

          window.open(`mailto:?subject=${encodeURIComponent(emailSubject)}&body=${encodeURIComponent(emailBody)}`);
          showNotification('✅ Email client opened with detailed report!');
          break;

        case 'whatsapp':
          // Share via WhatsApp with formatted message
          const whatsappText = `🏦 *Insurance Analysis Report*

👤 *Customer:* ${currentCustomer?.name}
📧 *Email:* ${currentCustomer?.email}
📅 *Date:* ${new Date().toLocaleDateString()}
📊 *Scenarios:* ${selectedScenarios.length}

💰 *Key Results:*
• Current Value: $187,000
• Premium Paid: $84,000
• ROI: 6.8%
• Net Gain: $103,000

✅ *Analysis completed successfully!*
📋 Comprehensive report with charts and recommendations available.`;

          window.open(`https://wa.me/?text=${encodeURIComponent(whatsappText)}`);
          showNotification('✅ WhatsApp opened with formatted report!');
          break;

        case 'print':
          // Add print-specific styling
          const printStyles = `
            @media print {
              .no-print { display: none !important; }
              .print-break { page-break-before: always; }
              body { font-size: 12pt; }
              h1 { font-size: 18pt; }
              h2 { font-size: 16pt; }
              h3 { font-size: 14pt; }
            }
          `;
          const styleSheet = document.createElement('style');
          styleSheet.textContent = printStyles;
          document.head.appendChild(styleSheet);

          window.print();

          // Remove print styles after printing
          setTimeout(() => {
            document.head.removeChild(styleSheet);
          }, 1000);

          showNotification('✅ Print dialog opened with optimized formatting!');
          break;

        default:
          break;
      }
    } catch (error) {
      console.error('Export error:', error);
      showNotification('❌ Export failed. Please try again.', false);
    }
  };

  const generateReportContent = () => {
    const reportDate = new Date().toLocaleDateString();
    const reportTime = new Date().toLocaleTimeString();

    return `INSURANCE ANALYSIS REPORT
=====================================

CUSTOMER INFORMATION
--------------------
Name: ${currentCustomer?.name || 'N/A'}
Email: ${currentCustomer?.email || 'N/A'}
Phone: ${currentCustomer?.phone || 'N/A'}
Customer ID: ${currentCustomer?.customer_id || 'N/A'}
Date of Birth: ${currentCustomer?.dob || 'N/A'}
Occupation: ${currentCustomer?.occupation || 'N/A'}
Annual Income: $${currentCustomer?.annual_income || 'N/A'}

REPORT DETAILS
--------------
Generated Date: ${reportDate}
Generated Time: ${reportTime}
Total Scenarios Analyzed: ${selectedScenarios.length}
Analysis Type: Comprehensive Policy Review

POLICY PERFORMANCE SUMMARY
---------------------------
${policyPerformanceData.map(row =>
  `Year ${row.year}: Current Value $${row.currentValue.toLocaleString()}, Projected $${row.projectedValue.toLocaleString()}, Premium Paid $${row.premiumPaid.toLocaleString()}`
).join('\n')}

SCENARIO COMPARISON
-------------------
${scenarioComparisonData.map(scenario =>
  `${scenario.scenario}: ROI ${scenario.roi}%, Risk Level ${scenario.riskLevel}, Projected Value $${scenario.projectedValue.toLocaleString()}`
).join('\n')}

POLICY BREAKDOWN
----------------
Death Benefit: $500,000 (62.5%)
Cash Value: $187,000 (23.4%)
Premium Paid: $84,000 (10.5%)
Dividends: $29,000 (3.6%)

This report was generated automatically by the Insurance Analysis System.
For questions or clarifications, please contact your insurance advisor.`;
  };

  const generateCSVContent = () => {
    const headers = ['Year', 'Current Value', 'Projected Value', 'Premium Paid', 'Growth Rate', 'Net Gain'];
    const csvData = policyPerformanceData.map((row, index) => {
      const growthRate = index > 0 ?
        (((row.currentValue - policyPerformanceData[index - 1].currentValue) / policyPerformanceData[index - 1].currentValue) * 100).toFixed(2) :
        '0.00';
      const netGain = row.currentValue - row.premiumPaid;
      return `${row.year},${row.currentValue},${row.projectedValue},${row.premiumPaid},${growthRate}%,${netGain}`;
    }).join('\n');

    const scenarioHeaders = ['Scenario', 'Risk Level', 'ROI %', 'Projected Value'];
    const scenarioData = scenarioComparisonData.map(scenario =>
      `${scenario.scenario},${scenario.riskLevel},${scenario.roi}%,${scenario.projectedValue}`
    ).join('\n');

    return `POLICY PERFORMANCE DATA\n${headers.join(',')}\n${csvData}\n\nSCENARIO COMPARISON DATA\n${scenarioHeaders.join(',')}\n${scenarioData}`;
  };

  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Analysis Reports</h1>
          <p className="text-gray-600 dark:text-gray-400">Comprehensive analysis of your selected insurance policy scenarios.</p>
        </div>

        {/* Backend Status Indicator */}
        <div className="flex items-center space-x-2">
          <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium ${
            backendStatus === 'connected' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
            backendStatus === 'disconnected' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
            'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
          }`}>
            {backendStatus === 'connected' && <CheckCircle className="w-4 h-4" />}
            {backendStatus === 'disconnected' && <XCircle className="w-4 h-4" />}
            {backendStatus === 'checking' && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>}
            <span>
              {backendStatus === 'connected' ? 'Backend Connected' :
               backendStatus === 'disconnected' ? 'Backend Offline' :
               'Checking Connection...'}
            </span>
          </div>
          {backendStatus === 'disconnected' && (
            <button
              onClick={checkBackendConnection}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              Retry
            </button>
          )}
        </div>
      </div>

      {/* Policy Holder Information */}
      {currentCustomer && (
        <Card>
          <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
              <User className="w-5 h-5 text-blue-600" />
              <span>Policy Holder Information</span>
            </h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Personal Information */}
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <h4 className="text-sm font-semibold text-blue-800 dark:text-blue-200 mb-3">Personal Information</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Name:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{currentCustomer?.name || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Email:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{currentCustomer?.email || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Phone:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{currentCustomer?.phone || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">DOB:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{currentCustomer?.dob || 'N/A'}</span>
                </div>
              </div>
            </div>

            {/* Financial Information */}
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <h4 className="text-sm font-semibold text-green-800 dark:text-green-200 mb-3">Financial Information</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Annual Income:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">${currentCustomer?.annual_income || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Occupation:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{currentCustomer?.occupation || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Customer ID:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{currentCustomer?.customer_id || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Address:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{currentCustomer?.address || 'N/A'}</span>
                </div>
              </div>
            </div>

            {/* Policy Summary */}
            <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
              <h4 className="text-sm font-semibold text-purple-800 dark:text-purple-200 mb-3">Policy Summary</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Total Policies:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{currentCustomer?.policies?.length || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Active Scenarios:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{selectedScenarios.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Report Date:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{new Date().toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Analysis Type:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Comprehensive</span>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex justify-center space-x-4">
              <Button
                onClick={handleGetIllustration}
                disabled={isLoading || selectedScenarios.length === 0}
                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Generating...</span>
                  </>
                ) : (
                  <>
                    <BarChart3 className="w-4 h-4" />
                    <span>Get Illustration</span>
                  </>
                )}
              </Button>

              {showReports && (
                <div className="relative">
                  <Button
                    onClick={() => setShowExportMenu(!showExportMenu)}
                    className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2"
                  >
                    <Download className="w-4 h-4" />
                    <span>Export</span>
                    <ChevronDown className="w-4 h-4" />
                  </Button>

                  {showExportMenu && (
                    <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-10">
                      <div className="py-2">
                        <button
                          onClick={() => handleExport('pdf')}
                          className="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2 text-gray-900 dark:text-gray-100"
                        >
                          <FileText className="w-4 h-4 text-red-500" />
                          <span>Download PDF</span>
                        </button>
                        <button
                          onClick={() => handleExport('csv')}
                          className="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2 text-gray-900 dark:text-gray-100"
                        >
                          <Table className="w-4 h-4 text-green-500" />
                          <span>Download CSV</span>
                        </button>
                        <hr className="my-1 border-gray-200 dark:border-gray-600" />
                        <button
                          onClick={() => handleExport('email')}
                          className="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2 text-gray-900 dark:text-gray-100"
                        >
                          <Mail className="w-4 h-4 text-blue-500" />
                          <span>Share via Email</span>
                        </button>
                        <button
                          onClick={() => handleExport('whatsapp')}
                          className="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2 text-gray-900 dark:text-gray-100"
                        >
                          <MessageCircle className="w-4 h-4 text-green-500" />
                          <span>Share via WhatsApp</span>
                        </button>
                        <hr className="my-1 border-gray-200 dark:border-gray-600" />
                        <button
                          onClick={() => handleExport('print')}
                          className="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2 text-gray-900 dark:text-gray-100"
                        >
                          <Printer className="w-4 h-4 text-gray-500" />
                          <span>Print Report</span>
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </Card>
      )}

      {/* No Scenarios Selected Warning */}
      {selectedScenarios.length === 0 && (
        <Card>
          <div className="text-center py-12">
            <AlertTriangle className="w-20 h-20 text-yellow-400 mx-auto mb-6" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No Scenarios Selected</h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">You need to select scenarios before generating analysis reports.</p>
            <div className="space-y-2">
              <p className="text-sm text-gray-400 dark:text-gray-500">1. Go to Selected Scenarios tab</p>
              <p className="text-sm text-gray-400 dark:text-gray-500">2. Choose scenarios to analyze</p>
              <p className="text-sm text-gray-400 dark:text-gray-500">3. Click "Get Illustration" button</p>
            </div>
          </div>
        </Card>
      )}

      {/* Scenarios Available */}
      {selectedScenarios.length > 0 && !showReports && (
        <Card>
          <div className="text-center py-12">
            <BarChart3 className="w-20 h-20 text-green-400 mx-auto mb-6" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Scenarios Ready for Analysis</h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              {selectedScenarios.length} scenario{selectedScenarios.length > 1 ? 's' : ''} selected for analysis.
            </p>
            <div className="space-y-2">
              <p className="text-sm text-gray-400 dark:text-gray-500">Click "Get Illustration" to generate comprehensive reports</p>
              <p className="text-sm text-gray-400 dark:text-gray-500">View detailed charts, tables, and analysis</p>
            </div>
          </div>
        </Card>
      )}

      {/* Comprehensive Reports Section */}
      {showReports && selectedScenarios.length > 0 && (
        <div className="space-y-6">
          {/* Data Table Report */}
          <Card>
            <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                <Table className="w-5 h-5 text-indigo-600" />
                <span>Policy Performance Data Table</span>
              </h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Year</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Current Value</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Projected Value</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Premium Paid</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Growth Rate</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Net Gain</th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                  {policyPerformanceData.map((row, index) => {
                    const growthRate = index > 0 ?
                      (((row.currentValue - policyPerformanceData[index - 1].currentValue) / policyPerformanceData[index - 1].currentValue) * 100).toFixed(2) :
                      '0.00';
                    const netGain = row.currentValue - row.premiumPaid;

                    return (
                      <tr key={row.year} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">{row.year}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${row.currentValue.toLocaleString()}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${row.projectedValue.toLocaleString()}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${row.premiumPaid.toLocaleString()}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            parseFloat(growthRate) > 0 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                          }`}>
                            {growthRate}%
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          <span className={`font-medium ${netGain > 0 ? 'text-green-600' : 'text-red-600'}`}>
                            ${netGain.toLocaleString()}
                          </span>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </Card>

          {/* Policy Performance Chart */}
          <Card>
            <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                <TrendingUp className="w-5 h-5 text-blue-600" />
                <span>Policy Performance Over Time</span>
              </h3>
            </div>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={policyPerformanceData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="year" />
                  <YAxis />
                  <Tooltip formatter={(value, name) => [`$${value.toLocaleString()}`, name]} />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="currentValue"
                    stroke="#3B82F6"
                    strokeWidth={3}
                    name="Current Value"
                  />
                  <Line
                    type="monotone"
                    dataKey="projectedValue"
                    stroke="#10B981"
                    strokeWidth={3}
                    strokeDasharray="5 5"
                    name="Projected Value"
                  />
                  <Line
                    type="monotone"
                    dataKey="premiumPaid"
                    stroke="#F59E0B"
                    strokeWidth={2}
                    name="Premium Paid"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </Card>

          {/* Scenario Comparison */}
          <Card>
            <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                <Target className="w-5 h-5 text-green-600" />
                <span>Scenario Comparison ({selectedScenarios.length} scenarios)</span>
              </h3>
            </div>

            {/* Scenario Comparison Table */}
            <div className="mb-6 overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Scenario</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Risk Level</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">ROI %</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Projected Value</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Recommendation</th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                  {scenarioComparisonData.map((scenario, index) => (
                    <tr key={scenario.scenario} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">{scenario.scenario}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          scenario.riskLevel === 'Low' ? 'bg-green-100 text-green-800' :
                          scenario.riskLevel === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {scenario.riskLevel}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        <span className="font-medium text-green-600">{scenario.roi}%</span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${scenario.projectedValue.toLocaleString()}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {index === 1 ? (
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                            Recommended
                          </span>
                        ) : (
                          <span className="text-gray-500">-</span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Scenario Comparison Chart */}
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={scenarioComparisonData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="scenario" />
                  <YAxis />
                  <Tooltip formatter={(value, name) => [
                    name === 'roi' ? `${value}%` : `$${value.toLocaleString()}`,
                    name === 'roi' ? 'ROI' : 'Projected Value'
                  ]} />
                  <Legend />
                  <Bar dataKey="roi" fill="#3B82F6" name="ROI %" />
                  <Bar dataKey="projectedValue" fill="#10B981" name="Projected Value ($)" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </Card>

          {/* Policy Breakdown and Monthly Premium Analysis */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Policy Breakdown Pie Chart */}
            <Card>
              <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                  <PieChart className="w-5 h-5 text-purple-600" />
                  <span>Policy Value Breakdown</span>
                </h3>
              </div>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsPieChart>
                    <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, '']} />
                    <Legend />
                    <Pie
                      data={policyBreakdownData}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      dataKey="value"
                      label={({ name, percentage }: any) => `${name}: ${percentage}%`}
                    >
                      {policyBreakdownData.map((entry: any, index: number) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                  </RechartsPieChart>
                </ResponsiveContainer>
              </div>

              {/* Policy Breakdown Summary */}
              <div className="mt-4 grid grid-cols-2 gap-4">
                {policyBreakdownData.map((item: any, index: number) => (
                  <div key={index} className="flex items-center space-x-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: item.color }}
                    ></div>
                    <div className="flex-1">
                      <p className="text-xs font-medium text-gray-900 dark:text-gray-100">{item.name}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">${item.value.toLocaleString()}</p>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Monthly Premium vs Claims */}
            <Card>
              <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                  <DollarSign className="w-5 h-5 text-green-600" />
                  <span>Monthly Premium vs Claims</span>
                </h3>
              </div>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={monthlyPremiumData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => [`$${value}`, '']} />
                    <Legend />
                    <Area
                      type="monotone"
                      dataKey="premium"
                      stackId="1"
                      stroke="#3B82F6"
                      fill="#3B82F6"
                      fillOpacity={0.6}
                      name="Premium"
                    />
                    <Area
                      type="monotone"
                      dataKey="claims"
                      stackId="2"
                      stroke="#EF4444"
                      fill="#EF4444"
                      fillOpacity={0.6}
                      name="Claims"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </Card>
          </div>

          {/* Summary Statistics */}
          <Card>
            <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                <BarChart3 className="w-5 h-5 text-indigo-600" />
                <span>Key Performance Indicators</span>
              </h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total Premium Paid</p>
                    <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">$84,000</p>
                  </div>
                  <DollarSign className="w-8 h-8 text-blue-600" />
                </div>
              </div>
              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-green-600 dark:text-green-400">Current Value</p>
                    <p className="text-2xl font-bold text-green-900 dark:text-green-100">$187,000</p>
                  </div>
                  <TrendingUp className="w-8 h-8 text-green-600" />
                </div>
              </div>
              <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Projected ROI</p>
                    <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">6.8%</p>
                  </div>
                  <Target className="w-8 h-8 text-purple-600" />
                </div>
              </div>
              <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-orange-600 dark:text-orange-400">Years to Maturity</p>
                    <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">15</p>
                  </div>
                  <Calendar className="w-8 h-8 text-orange-600" />
                </div>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

export default AnalysisReports;