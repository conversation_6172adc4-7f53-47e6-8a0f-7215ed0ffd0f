import React, { useState } from 'react';
import Card from '../common/Card';
import Button from '../common/Button';
import { useDashboard } from '../../contexts/DashboardContext';
import {
  BarChart3,
  User,
  AlertTriangle,
  Download,
  FileText,
  Mail,
  MessageCircle,
  Printer,
  Share2,
  TrendingUp,
  PieChart,
  Table,
  Target,
  DollarSign,
  Calendar,
  ChevronDown
} from 'lucide-react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

const AnalysisReports: React.FC = () => {
  const { selectedScenarios, currentCustomer } = useDashboard();
  const [isLoading, setIsLoading] = useState(false);
  const [showReports, setShowReports] = useState(false);
  const [showExportMenu, setShowExportMenu] = useState(false);

  const handleGetIllustration = async () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      setShowReports(true);
    }, 2000);
  };

  // Sample data for charts
  const policyPerformanceData = [
    { year: '2024', currentValue: 100000, projectedValue: 105000, premiumPaid: 12000 },
    { year: '2025', currentValue: 112000, projectedValue: 118000, premiumPaid: 24000 },
    { year: '2026', currentValue: 125000, projectedValue: 132000, premiumPaid: 36000 },
    { year: '2027', currentValue: 139000, projectedValue: 147000, premiumPaid: 48000 },
    { year: '2028', currentValue: 154000, projectedValue: 163000, premiumPaid: 60000 },
    { year: '2029', currentValue: 170000, projectedValue: 180000, premiumPaid: 72000 },
    { year: '2030', currentValue: 187000, projectedValue: 198000, premiumPaid: 84000 },
  ];

  const scenarioComparisonData = [
    { scenario: 'Conservative', roi: 4.2, riskLevel: 'Low', projectedValue: 180000 },
    { scenario: 'Moderate', roi: 6.8, riskLevel: 'Medium', projectedValue: 220000 },
    { scenario: 'Aggressive', roi: 9.1, riskLevel: 'High', projectedValue: 280000 },
  ];

  // Policy breakdown data for pie chart
  const policyBreakdownData = [
    { name: 'Death Benefit', value: 500000, color: '#3B82F6', percentage: 62.5 },
    { name: 'Cash Value', value: 187000, color: '#10B981', percentage: 23.4 },
    { name: 'Premium Paid', value: 84000, color: '#F59E0B', percentage: 10.5 },
    { name: 'Dividends', value: 29000, color: '#8B5CF6', percentage: 3.6 },
  ];

  const monthlyPremiumData = [
    { month: 'Jan', premium: 1000, claims: 200 },
    { month: 'Feb', premium: 1000, claims: 150 },
    { month: 'Mar', premium: 1000, claims: 300 },
    { month: 'Apr', premium: 1000, claims: 180 },
    { month: 'May', premium: 1000, claims: 220 },
    { month: 'Jun', premium: 1000, claims: 160 },
  ];

  const handleExport = (type: string) => {
    setShowExportMenu(false);

    switch (type) {
      case 'pdf':
        // Create PDF export
        const pdfContent = generateReportContent();
        downloadFile(pdfContent, 'analysis-report.pdf', 'application/pdf');
        break;
      case 'csv':
        // Create CSV export
        const csvContent = generateCSVContent();
        downloadFile(csvContent, 'analysis-report.csv', 'text/csv');
        break;
      case 'email':
        // Open email client with report data
        const emailSubject = `Insurance Analysis Report - ${currentCustomer?.name}`;
        const emailBody = `Please find the insurance analysis report attached.\n\nCustomer: ${currentCustomer?.name}\nPolicy Analysis Date: ${new Date().toLocaleDateString()}`;
        window.open(`mailto:?subject=${encodeURIComponent(emailSubject)}&body=${encodeURIComponent(emailBody)}`);
        break;
      case 'whatsapp':
        // Share via WhatsApp
        const whatsappText = `Insurance Analysis Report for ${currentCustomer?.name}\nGenerated on: ${new Date().toLocaleDateString()}\nTotal Scenarios: ${selectedScenarios.length}`;
        window.open(`https://wa.me/?text=${encodeURIComponent(whatsappText)}`);
        break;
      case 'print':
        window.print();
        break;
      default:
        break;
    }
  };

  const generateReportContent = () => {
    return `Insurance Analysis Report
Customer: ${currentCustomer?.name}
Email: ${currentCustomer?.email}
Generated: ${new Date().toLocaleDateString()}
Scenarios Analyzed: ${selectedScenarios.length}`;
  };

  const generateCSVContent = () => {
    const headers = ['Year', 'Current Value', 'Projected Value', 'Premium Paid'];
    const csvData = policyPerformanceData.map(row =>
      `${row.year},${row.currentValue},${row.projectedValue},${row.premiumPaid}`
    ).join('\n');
    return `${headers.join(',')}\n${csvData}`;
  };

  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Analysis Reports</h1>
        <p className="text-gray-600 dark:text-gray-400">Comprehensive analysis of your selected insurance policy scenarios.</p>
      </div>

      {/* Policy Holder Information */}
      {currentCustomer && (
        <Card>
          <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
              <User className="w-5 h-5 text-blue-600" />
              <span>Policy Holder Information</span>
            </h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Personal Information */}
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <h4 className="text-sm font-semibold text-blue-800 dark:text-blue-200 mb-3">Personal Information</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Name:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{currentCustomer?.name || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Email:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{currentCustomer?.email || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Phone:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{currentCustomer?.phone || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">DOB:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{currentCustomer?.dob || 'N/A'}</span>
                </div>
              </div>
            </div>

            {/* Financial Information */}
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <h4 className="text-sm font-semibold text-green-800 dark:text-green-200 mb-3">Financial Information</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Annual Income:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">${currentCustomer?.annual_income || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Occupation:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{currentCustomer?.occupation || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Customer ID:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{currentCustomer?.customer_id || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Address:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{currentCustomer?.address || 'N/A'}</span>
                </div>
              </div>
            </div>

            {/* Policy Summary */}
            <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
              <h4 className="text-sm font-semibold text-purple-800 dark:text-purple-200 mb-3">Policy Summary</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Total Policies:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{currentCustomer?.policies?.length || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Active Scenarios:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{selectedScenarios.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Report Date:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{new Date().toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Analysis Type:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Comprehensive</span>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex justify-center space-x-4">
              <Button
                onClick={handleGetIllustration}
                disabled={isLoading || selectedScenarios.length === 0}
                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Generating...</span>
                  </>
                ) : (
                  <>
                    <BarChart3 className="w-4 h-4" />
                    <span>Get Illustration</span>
                  </>
                )}
              </Button>

              {showReports && (
                <div className="relative">
                  <Button
                    onClick={() => setShowExportMenu(!showExportMenu)}
                    className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2"
                  >
                    <Download className="w-4 h-4" />
                    <span>Export</span>
                    <ChevronDown className="w-4 h-4" />
                  </Button>

                  {showExportMenu && (
                    <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-10">
                      <div className="py-2">
                        <button
                          onClick={() => handleExport('pdf')}
                          className="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2 text-gray-900 dark:text-gray-100"
                        >
                          <FileText className="w-4 h-4 text-red-500" />
                          <span>Download PDF</span>
                        </button>
                        <button
                          onClick={() => handleExport('csv')}
                          className="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2 text-gray-900 dark:text-gray-100"
                        >
                          <Table className="w-4 h-4 text-green-500" />
                          <span>Download CSV</span>
                        </button>
                        <hr className="my-1 border-gray-200 dark:border-gray-600" />
                        <button
                          onClick={() => handleExport('email')}
                          className="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2 text-gray-900 dark:text-gray-100"
                        >
                          <Mail className="w-4 h-4 text-blue-500" />
                          <span>Share via Email</span>
                        </button>
                        <button
                          onClick={() => handleExport('whatsapp')}
                          className="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2 text-gray-900 dark:text-gray-100"
                        >
                          <MessageCircle className="w-4 h-4 text-green-500" />
                          <span>Share via WhatsApp</span>
                        </button>
                        <hr className="my-1 border-gray-200 dark:border-gray-600" />
                        <button
                          onClick={() => handleExport('print')}
                          className="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2 text-gray-900 dark:text-gray-100"
                        >
                          <Printer className="w-4 h-4 text-gray-500" />
                          <span>Print Report</span>
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </Card>
      )}

      {/* No Scenarios Selected Warning */}
      {selectedScenarios.length === 0 && (
        <Card>
          <div className="text-center py-12">
            <AlertTriangle className="w-20 h-20 text-yellow-400 mx-auto mb-6" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No Scenarios Selected</h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">You need to select scenarios before generating analysis reports.</p>
            <div className="space-y-2">
              <p className="text-sm text-gray-400 dark:text-gray-500">1. Go to Selected Scenarios tab</p>
              <p className="text-sm text-gray-400 dark:text-gray-500">2. Choose scenarios to analyze</p>
              <p className="text-sm text-gray-400 dark:text-gray-500">3. Click "Get Illustration" button</p>
            </div>
          </div>
        </Card>
      )}

      {/* Scenarios Available */}
      {selectedScenarios.length > 0 && !showReports && (
        <Card>
          <div className="text-center py-12">
            <BarChart3 className="w-20 h-20 text-green-400 mx-auto mb-6" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Scenarios Ready for Analysis</h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              {selectedScenarios.length} scenario{selectedScenarios.length > 1 ? 's' : ''} selected for analysis.
            </p>
            <div className="space-y-2">
              <p className="text-sm text-gray-400 dark:text-gray-500">Click "Get Illustration" to generate comprehensive reports</p>
              <p className="text-sm text-gray-400 dark:text-gray-500">View detailed charts, tables, and analysis</p>
            </div>
          </div>
        </Card>
      )}

      {/* Comprehensive Reports Section */}
      {showReports && selectedScenarios.length > 0 && (
        <div className="space-y-6">
          {/* Data Table Report */}
          <Card>
            <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                <Table className="w-5 h-5 text-indigo-600" />
                <span>Policy Performance Data Table</span>
              </h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Year</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Current Value</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Projected Value</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Premium Paid</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Growth Rate</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Net Gain</th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                  {policyPerformanceData.map((row, index) => {
                    const growthRate = index > 0 ?
                      (((row.currentValue - policyPerformanceData[index - 1].currentValue) / policyPerformanceData[index - 1].currentValue) * 100).toFixed(2) :
                      '0.00';
                    const netGain = row.currentValue - row.premiumPaid;

                    return (
                      <tr key={row.year} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">{row.year}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${row.currentValue.toLocaleString()}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${row.projectedValue.toLocaleString()}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${row.premiumPaid.toLocaleString()}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            parseFloat(growthRate) > 0 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                          }`}>
                            {growthRate}%
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          <span className={`font-medium ${netGain > 0 ? 'text-green-600' : 'text-red-600'}`}>
                            ${netGain.toLocaleString()}
                          </span>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </Card>

          {/* Policy Performance Chart */}
          <Card>
            <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                <TrendingUp className="w-5 h-5 text-blue-600" />
                <span>Policy Performance Over Time</span>
              </h3>
            </div>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={policyPerformanceData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="year" />
                  <YAxis />
                  <Tooltip formatter={(value, name) => [`$${value.toLocaleString()}`, name]} />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="currentValue"
                    stroke="#3B82F6"
                    strokeWidth={3}
                    name="Current Value"
                  />
                  <Line
                    type="monotone"
                    dataKey="projectedValue"
                    stroke="#10B981"
                    strokeWidth={3}
                    strokeDasharray="5 5"
                    name="Projected Value"
                  />
                  <Line
                    type="monotone"
                    dataKey="premiumPaid"
                    stroke="#F59E0B"
                    strokeWidth={2}
                    name="Premium Paid"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </Card>

          {/* Scenario Comparison */}
          <Card>
            <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                <Target className="w-5 h-5 text-green-600" />
                <span>Scenario Comparison ({selectedScenarios.length} scenarios)</span>
              </h3>
            </div>

            {/* Scenario Comparison Table */}
            <div className="mb-6 overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Scenario</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Risk Level</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">ROI %</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Projected Value</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Recommendation</th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                  {scenarioComparisonData.map((scenario, index) => (
                    <tr key={scenario.scenario} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">{scenario.scenario}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          scenario.riskLevel === 'Low' ? 'bg-green-100 text-green-800' :
                          scenario.riskLevel === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {scenario.riskLevel}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        <span className="font-medium text-green-600">{scenario.roi}%</span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${scenario.projectedValue.toLocaleString()}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {index === 1 ? (
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                            Recommended
                          </span>
                        ) : (
                          <span className="text-gray-500">-</span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Scenario Comparison Chart */}
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={scenarioComparisonData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="scenario" />
                  <YAxis />
                  <Tooltip formatter={(value, name) => [
                    name === 'roi' ? `${value}%` : `$${value.toLocaleString()}`,
                    name === 'roi' ? 'ROI' : 'Projected Value'
                  ]} />
                  <Legend />
                  <Bar dataKey="roi" fill="#3B82F6" name="ROI %" />
                  <Bar dataKey="projectedValue" fill="#10B981" name="Projected Value ($)" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </Card>

          {/* Policy Breakdown and Monthly Premium Analysis */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Policy Breakdown Pie Chart */}
            <Card>
              <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                  <PieChart className="w-5 h-5 text-purple-600" />
                  <span>Policy Value Breakdown</span>
                </h3>
              </div>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsPieChart>
                    <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, '']} />
                    <Legend />
                    <Pie
                      data={policyBreakdownData}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      dataKey="value"
                      label={({ name, percentage }: any) => `${name}: ${percentage}%`}
                    >
                      {policyBreakdownData.map((entry: any, index: number) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                  </RechartsPieChart>
                </ResponsiveContainer>
              </div>

              {/* Policy Breakdown Summary */}
              <div className="mt-4 grid grid-cols-2 gap-4">
                {policyBreakdownData.map((item: any, index: number) => (
                  <div key={index} className="flex items-center space-x-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: item.color }}
                    ></div>
                    <div className="flex-1">
                      <p className="text-xs font-medium text-gray-900 dark:text-gray-100">{item.name}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">${item.value.toLocaleString()}</p>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Monthly Premium vs Claims */}
            <Card>
              <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                  <DollarSign className="w-5 h-5 text-green-600" />
                  <span>Monthly Premium vs Claims</span>
                </h3>
              </div>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={monthlyPremiumData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => [`$${value}`, '']} />
                    <Legend />
                    <Area
                      type="monotone"
                      dataKey="premium"
                      stackId="1"
                      stroke="#3B82F6"
                      fill="#3B82F6"
                      fillOpacity={0.6}
                      name="Premium"
                    />
                    <Area
                      type="monotone"
                      dataKey="claims"
                      stackId="2"
                      stroke="#EF4444"
                      fill="#EF4444"
                      fillOpacity={0.6}
                      name="Claims"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </Card>
          </div>

          {/* Summary Statistics */}
          <Card>
            <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                <BarChart3 className="w-5 h-5 text-indigo-600" />
                <span>Key Performance Indicators</span>
              </h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total Premium Paid</p>
                    <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">$84,000</p>
                  </div>
                  <DollarSign className="w-8 h-8 text-blue-600" />
                </div>
              </div>
              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-green-600 dark:text-green-400">Current Value</p>
                    <p className="text-2xl font-bold text-green-900 dark:text-green-100">$187,000</p>
                  </div>
                  <TrendingUp className="w-8 h-8 text-green-600" />
                </div>
              </div>
              <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Projected ROI</p>
                    <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">6.8%</p>
                  </div>
                  <Target className="w-8 h-8 text-purple-600" />
                </div>
              </div>
              <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-orange-600 dark:text-orange-400">Years to Maturity</p>
                    <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">15</p>
                  </div>
                  <Calendar className="w-8 h-8 text-orange-600" />
                </div>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

export default AnalysisReports;