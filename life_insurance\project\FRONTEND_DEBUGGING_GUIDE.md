# Frontend Debugging Guide - Policy Holder Filtering

## 🔍 **Issue: Still Showing All Scenarios Instead of Filtered**

You're seeing both <PERSON> and <PERSON> scenarios, but you should only see <PERSON>'s scenarios when he's the selected policy holder.

## 🛠️ **Debugging Steps**

### **Step 1: Check Browser localStorage**
1. Open browser Developer Tools (F12)
2. Go to **Application** tab → **Local Storage** → `http://localhost:5174`
3. Look for key: `dashboardContext_policyData`
4. Check if it contains <PERSON>'s data:
   ```json
   {
     "selectedCustomerData": {"name": "<PERSON>"},
     "selectedPolicyData": {"policy_number": "POL-12345678"},
     "currentCustomer": {"customer_id": "CUS-567890"},
     "policySearchPerformed": true
   }
   ```

### **Step 2: Check Console Logs**
1. Open browser Developer Tools (F12)
2. Go to **Console** tab
3. Refresh the page and look for these logs:
   - `🔄 Restoring policy search data from localStorage:`
   - `🔍 Auto-filtering scenarios for current policy holder:`
   - `📊 Loaded X scenarios for current policy holder`

### **Step 3: Manual Reset and Test**
1. **Clear localStorage**: In Console, run:
   ```javascript
   localStorage.clear()
   ```
2. **Refresh page** (F5)
3. **Search for John Smith**:
   - Go to Policy Selection
   - Search: John Smith, POL-12345678, CUS-567890
4. **Go to Selected Scenarios tab**
5. **Check if filtering is now working**

## 🎯 **Expected Behavior After Fix**

### **When Policy Holder is Selected:**
- ✅ **Policy Holder Filter section** shows John Smith's information
- ✅ **Blue status bar** shows "Showing scenarios for: John Smith"
- ✅ **Only John Smith's scenarios** are displayed
- ✅ **Scenario count** shows correct number (e.g., "2 scenario(s) found")

### **Available Actions:**
- 🔵 **"Show Only This Policy"** button - Re-applies filter for current policy holder
- 🔴 **"Clear Filter"** button - Removes filter and shows all scenarios
- ⚪ **"Show All Scenarios"** button - Shows all scenarios without clearing localStorage

## 🚀 **Quick Fix Options**

### **Option 1: Force Clear and Restart**
```javascript
// Run in browser console
localStorage.clear();
window.location.reload();
```

### **Option 2: Manual Filter Test**
1. Go to Selected Scenarios tab
2. Click **"Show All Scenarios"** button (should show all 5 scenarios)
3. Click **"Show Only This Policy"** button (should show only John Smith's scenarios)

### **Option 3: Re-search Policy Holder**
1. Go to Policy Selection tab
2. Search for John Smith again
3. Go back to Selected Scenarios tab
4. Should now show filtered results

## 🔧 **Technical Details**

### **What Changed:**
1. **DashboardContext** now automatically filters scenarios on page load
2. **localStorage** restoration happens before scenario loading
3. **Automatic filtering** applied if policy data exists
4. **Visual indicators** added to show filtering status

### **Filtering Priority:**
1. **Policy Number** (most specific) - POL-12345678
2. **Customer ID** (fallback) - CUS-567890  
3. **Customer Name** (fallback) - John Smith

### **Debug API Calls:**
You can test the filtering directly:
```bash
# All scenarios
curl -H "Authorization: Basic ZGVtbzpkZW1vMTIz" http://localhost:8000/api/scenarios

# John Smith only
curl -H "Authorization: Basic ZGVtbzpkZW1vMTIz" "http://localhost:8000/api/scenarios?policy_number=POL-12345678"
```

## 📊 **Current Database Status**

Based on our tests:
- ✅ **Total scenarios**: 5
- ✅ **John Smith scenarios**: 2 (POL-12345678)
- ✅ **Jane Doe scenarios**: 1 (POL-87654321)
- ✅ **Legacy scenarios**: 2 (no policy info)

## 🎯 **Success Indicators**

### **You'll know it's working when:**
1. ✅ **Blue filter status bar** appears
2. ✅ **Only 2 scenarios** shown for John Smith
3. ✅ **Policy holder info** displayed correctly
4. ✅ **Filter buttons** work as expected

### **If still not working:**
1. Check browser console for errors
2. Verify localStorage contains correct data
3. Try manual filter buttons
4. Clear localStorage and re-search

## 💡 **Pro Tips**

### **For Testing:**
- Use **"Clear Filter"** button to reset state
- Use **"Show Only This Policy"** to force filtering
- Check console logs for debugging info

### **For Production:**
- Filtering happens automatically on page load
- No manual action needed after policy search
- Data persists across browser sessions

**The filtering should now work automatically! If you're still seeing issues, try the debugging steps above.** 🚀
