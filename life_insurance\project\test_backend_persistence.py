#!/usr/bin/env python3
"""
Test script to verify backend persistence and data loading after refresh
"""

import requests
import json
import base64
from datetime import datetime

# API Configuration
BASE_URL = "http://localhost:8000"
USERNAME = "demo"
PASSWORD = "demo123"

def create_auth_header():
    """Create Basic Auth header"""
    credentials = f"{USERNAME}:{PASSWORD}"
    encoded = base64.b64encode(credentials.encode()).decode()
    return {"Authorization": f"Basic {encoded}"}

def test_backend_health():
    """Test if backend is running"""
    print("🔍 Testing backend health...")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Backend is healthy: {result}")
            return True
        else:
            print(f"❌ Backend health check failed: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Backend connection failed: {e}")
        return False

def test_scenarios_persistence():
    """Test scenarios backend persistence"""
    print("\n🔍 Testing scenarios persistence...")
    
    headers = create_auth_header()
    
    # Get current scenarios
    response = requests.get(f"{BASE_URL}/api/scenarios", headers=headers)
    print(f"GET scenarios status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        scenarios = result.get('scenarios', [])
        print(f"✅ Found {len(scenarios)} scenarios in database")
        
        # Show As-Is scenarios
        as_is_scenarios = [s for s in scenarios if s.get('category') == 'as-is']
        print(f"✅ Found {len(as_is_scenarios)} As-Is scenarios")
        
        for scenario in as_is_scenarios[:3]:  # Show first 3
            print(f"   - {scenario['name']} (Created: {scenario.get('createdAt', 'N/A')})")
        
        return True
    else:
        print(f"❌ Failed to get scenarios: {response.text}")
        return False

def test_selected_scenarios_persistence():
    """Test selected scenarios persistence"""
    print("\n🔍 Testing selected scenarios persistence...")
    
    headers = create_auth_header()
    
    response = requests.get(f"{BASE_URL}/api/scenarios/selected", headers=headers)
    print(f"GET selected scenarios status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        selected_ids = result.get('selectedScenarios', [])
        print(f"✅ Found {len(selected_ids)} selected scenarios")
        print(f"   Selected IDs: {selected_ids}")
        return True
    else:
        print(f"❌ Failed to get selected scenarios: {response.text}")
        return False

def test_policy_search():
    """Test policy search functionality"""
    print("\n🔍 Testing policy search...")
    
    headers = {
        "Content-Type": "application/json",
        **create_auth_header()
    }
    
    # Test with known customer data
    search_data = {
        "customer_id": "CUS-567890",
        "policy_number": "POL-12345678",
        "customer_name": "John Smith"
    }
    
    response = requests.post(
        f"{BASE_URL}/api/policies/search",
        headers=headers,
        json=search_data
    )
    
    print(f"Policy search status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Policy search successful: {result.get('found', False)}")
        if result.get('found') and result.get('customer'):
            customer = result['customer']
            print(f"   Customer: {customer.get('name', 'N/A')}")
            print(f"   Policies: {len(customer.get('policies', []))}")
        return True
    else:
        print(f"❌ Policy search failed: {response.text}")
        return False

def test_create_and_retrieve_scenario():
    """Test creating a scenario and retrieving it (simulates page refresh)"""
    print("\n🔍 Testing create and retrieve scenario (simulates refresh)...")
    
    headers = {
        "Content-Type": "application/json",
        **create_auth_header()
    }
    
    # Create a test scenario
    test_scenario = {
        "name": f"Test Persistence - {datetime.now().strftime('%H:%M:%S')}",
        "category": "as-is",
        "asIsDetails": "Test persistence after refresh",
        "whatIfOptions": ["Test option 1", "Test option 2"]
    }
    
    # Create scenario
    create_response = requests.post(
        f"{BASE_URL}/api/scenarios",
        headers=headers,
        json=test_scenario
    )
    
    print(f"Create scenario status: {create_response.status_code}")
    
    if create_response.status_code == 201:
        result = create_response.json()
        scenario_id = result.get('id')
        print(f"✅ Scenario created with ID: {scenario_id}")
        
        # Now retrieve all scenarios (simulates page refresh)
        get_response = requests.get(f"{BASE_URL}/api/scenarios", headers=create_auth_header())
        
        if get_response.status_code == 200:
            scenarios_data = get_response.json()
            scenarios = scenarios_data.get('scenarios', [])
            
            # Find our created scenario
            found_scenario = None
            for scenario in scenarios:
                if scenario.get('id') == scenario_id:
                    found_scenario = scenario
                    break
            
            if found_scenario:
                print(f"✅ Scenario persisted and retrieved after 'refresh'")
                print(f"   Name: {found_scenario['name']}")
                print(f"   Category: {found_scenario['category']}")
                return True
            else:
                print(f"❌ Scenario not found after 'refresh'")
                return False
        else:
            print(f"❌ Failed to retrieve scenarios after creation")
            return False
    else:
        print(f"❌ Failed to create scenario: {create_response.text}")
        return False

def main():
    """Run all persistence tests"""
    print("🚀 Testing Backend Persistence and Data Loading")
    print("=" * 60)
    
    tests = [
        ("Backend Health", test_backend_health),
        ("Scenarios Persistence", test_scenarios_persistence),
        ("Selected Scenarios Persistence", test_selected_scenarios_persistence),
        ("Policy Search", test_policy_search),
        ("Create and Retrieve (Refresh Test)", test_create_and_retrieve_scenario)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    print("\n📊 Test Results Summary:")
    print("=" * 40)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} - {test_name}")
        if not passed:
            all_passed = False
    
    print("\n🎯 Overall Result:")
    if all_passed:
        print("✅ ALL TESTS PASSED - Backend persistence is working correctly!")
        print("   - As-Is configurations will persist after page refresh")
        print("   - Selected scenarios will persist after page refresh")
        print("   - Policy search data is properly connected to backend")
    else:
        print("❌ SOME TESTS FAILED - There may be persistence issues")
        print("   - Check backend connection and database")
        print("   - Verify API endpoints are working correctly")
    
    print("\n💡 To verify in the UI:")
    print("1. Save an As-Is configuration")
    print("2. Go to Selected Scenarios tab and select some scenarios")
    print("3. Refresh the page (F5)")
    print("4. Check if data is still there")

if __name__ == "__main__":
    main()
