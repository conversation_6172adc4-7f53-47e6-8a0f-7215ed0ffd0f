#!/usr/bin/env python3
"""
Test script to verify As-Is configurations are saved to Selected Scenarios tab only
"""

import requests
import json
import base64
from datetime import datetime

# API Configuration
BASE_URL = "http://localhost:8000"
USERNAME = "demo"
PASSWORD = "demo123"

def create_auth_header():
    """Create Basic Auth header"""
    credentials = f"{USERNAME}:{PASSWORD}"
    encoded = base64.b64encode(credentials.encode()).decode()
    return {"Authorization": f"Basic {encoded}"}

def test_create_as_is_scenario():
    """Test creating an As-Is scenario"""
    print("🔍 Testing As-Is scenario creation...")
    
    headers = {
        "Content-Type": "application/json",
        **create_auth_header()
    }
    
    test_data = {
        "name": "As-Is Configuration - Test Customer (POL-TEST-002)",
        "category": "as-is",
        "asIsDetails": "Retirement Age: 67, Maturity Age: 121",
        "whatIfOptions": [
            "Face Amount: 1,000,000",
            "Annual Premium: 12,000"
        ],
        "data": {
            "policyData": {
                "policyNumber": "POL-TEST-002",
                "customerName": "Test Customer",
                "customerId": "CUS-TEST-002",
                "faceAmount": "1000000",
                "annualPremium": "12000",
                "currentAge": "35"
            },
            "illustrationScenarios": {
                "retirementGoalAge": "67",
                "maturityAge": "121",
                "modelRetirementGoal": True,
                "confirmMaturityAge": True
            }
        }
    }
    
    response = requests.post(
        f"{BASE_URL}/api/scenarios",
        headers=headers,
        json=test_data
    )
    
    print(f"Status: {response.status_code}")
    if response.status_code == 201:
        result = response.json()
        print(f"✅ As-Is scenario created successfully!")
        print(f"   ID: {result.get('id')}")
        return result.get("id")
    else:
        print(f"❌ Error: {response.text}")
        return None

def test_get_scenarios_by_category():
    """Test getting scenarios filtered by category"""
    print("\n🔍 Testing scenarios retrieval by category...")
    
    headers = create_auth_header()
    
    response = requests.get(
        f"{BASE_URL}/api/scenarios",
        headers=headers
    )
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        scenarios = result.get('scenarios', [])
        
        # Filter by category
        as_is_scenarios = [s for s in scenarios if s.get('category') == 'as-is']
        other_scenarios = [s for s in scenarios if s.get('category') != 'as-is']
        
        print(f"📊 Total scenarios: {len(scenarios)}")
        print(f"📊 As-Is scenarios: {len(as_is_scenarios)}")
        print(f"📊 Other scenarios: {len(other_scenarios)}")
        
        print("\n📋 As-Is Scenarios:")
        for scenario in as_is_scenarios:
            print(f"   - {scenario['name']}")
            print(f"     Details: {scenario.get('asIsDetails', 'N/A')}")
            print(f"     Created: {scenario.get('createdAt', 'N/A')}")
        
        return as_is_scenarios
    else:
        print(f"❌ Error: {response.text}")
        return []

def test_select_scenario():
    """Test selecting a scenario"""
    print("\n🔍 Testing scenario selection...")
    
    # First get all scenarios to find an As-Is one
    scenarios = test_get_scenarios_by_category()
    if not scenarios:
        print("❌ No As-Is scenarios found to select")
        return
    
    scenario_id = scenarios[0]['id']
    
    headers = {
        "Content-Type": "application/json",
        **create_auth_header()
    }
    
    response = requests.post(
        f"{BASE_URL}/api/scenarios/selected",
        headers=headers,
        json={"scenarioIds": [scenario_id]}
    )
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        print(f"✅ Scenario selected successfully!")
        print(f"   Selected scenario ID: {scenario_id}")
        
        # Verify selection
        response = requests.get(
            f"{BASE_URL}/api/scenarios/selected",
            headers=create_auth_header()
        )
        
        if response.status_code == 200:
            result = response.json()
            selected_ids = result.get('selectedScenarios', [])
            print(f"   Currently selected scenarios: {len(selected_ids)}")
            if scenario_id in selected_ids:
                print(f"   ✅ As-Is scenario is properly selected!")
            else:
                print(f"   ⚠️  As-Is scenario not found in selected list")
        
    else:
        print(f"❌ Error selecting scenario: {response.text}")

def main():
    """Run As-Is scenario tests"""
    print("🚀 Starting As-Is Scenario Tests")
    print("=" * 50)
    
    # Test creating an As-Is scenario
    scenario_id = test_create_as_is_scenario()
    
    # Test getting scenarios by category
    as_is_scenarios = test_get_scenarios_by_category()
    
    # Test selecting an As-Is scenario
    test_select_scenario()
    
    print("\n📊 Summary:")
    print(f"  - As-Is scenarios found: {len(as_is_scenarios)}")
    print(f"  - New scenario created: {'Yes' if scenario_id else 'No'}")
    
    print("\n✅ As-Is scenario tests completed!")
    print("\n💡 Next steps:")
    print("  1. Go to the As-Is Illustration tab")
    print("  2. Fill out the form and click 'Save AS-IS Illustration'")
    print("  3. Go to the Selected Scenarios tab")
    print("  4. Filter by 'As-Is Configuration' to see your saved data")
    print("  5. Select scenarios for analysis")

if __name__ == "__main__":
    main()
