#!/usr/bin/env python3
"""
Test script to verify policy search data persistence
"""

import requests
import json
import base64
from datetime import datetime

# API Configuration
BASE_URL = "http://localhost:8000"
USERNAME = "demo"
PASSWORD = "demo123"

def create_auth_header():
    """Create Basic Auth header"""
    credentials = f"{USERNAME}:{PASSWORD}"
    encoded = base64.b64encode(credentials.encode()).decode()
    return {"Authorization": f"Basic {encoded}"}

def test_policy_search_and_persistence():
    """Test policy search and verify data structure"""
    print("🔍 Testing policy search for persistence...")
    
    headers = {
        "Content-Type": "application/json",
        **create_auth_header()
    }
    
    # Test with known customer data
    search_data = {
        "customer_id": "CUS-567890",
        "policy_number": "POL-12345678",
        "customer_name": "<PERSON>"
    }
    
    response = requests.post(
        f"{BASE_URL}/api/policies/search",
        headers=headers,
        json=search_data
    )
    
    print(f"Policy search status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Policy search successful: {result.get('found', False)}")
        
        if result.get('found') and result.get('customer'):
            customer = result['customer']
            print(f"\n📋 Customer Data Structure:")
            print(f"   Name: {customer.get('name', 'N/A')}")
            print(f"   Customer ID: {customer.get('customer_id', 'N/A')}")
            print(f"   DOB: {customer.get('dob', 'N/A')}")
            print(f"   Email: {customer.get('email', 'N/A')}")
            print(f"   Phone: {customer.get('phone', 'N/A')}")
            print(f"   Policies: {len(customer.get('policies', []))}")
            
            # Show first policy details
            policies = customer.get('policies', [])
            if policies:
                policy = policies[0]
                print(f"\n📋 First Policy Data Structure:")
                print(f"   Policy Number: {policy.get('policy_number', 'N/A')}")
                print(f"   Policy Type: {policy.get('policy_type', 'N/A')}")
                print(f"   Status: {policy.get('status', 'N/A')}")
                
                policy_details = policy.get('policy_details', {})
                print(f"   Coverage: {policy_details.get('coverage', 'N/A')}")
                print(f"   Premium: {policy_details.get('premium', 'N/A')}")
                print(f"   Start Date: {policy_details.get('start_date', 'N/A')}")
            
            print(f"\n💾 This data should now persist in localStorage after frontend updates")
            return True
        else:
            print(f"❌ Customer not found: {result.get('message', 'Unknown error')}")
            return False
    else:
        print(f"❌ Policy search failed: {response.text}")
        return False

def test_scenarios_with_policy_data():
    """Test creating scenarios with policy data"""
    print("\n🔍 Testing scenario creation with policy data...")
    
    headers = {
        "Content-Type": "application/json",
        **create_auth_header()
    }
    
    # Create a scenario with detailed policy data
    scenario_data = {
        "name": f"As-Is with Policy Data - {datetime.now().strftime('%H:%M:%S')}",
        "category": "as-is",
        "asIsDetails": "Retirement Age: 65, Maturity Age: 121",
        "whatIfOptions": [
            "Face Amount: 500,000",
            "Annual Premium: 2,000"
        ],
        "data": {
            "policyData": {
                "policyNumber": "POL-12345678",
                "customerName": "John Smith",
                "customerId": "CUS-567890",
                "policyType": "whole-life-insurance",
                "faceAmount": "500000",
                "annualPremium": "2000",
                "paymentPeriod": "20",
                "dividendOption": "Paid-up Additions",
                "currentAge": "31",
                "retirementAge": "65",
                "lifeExpectancy": "85"
            },
            "illustrationScenarios": {
                "modelRetirementGoal": True,
                "retirementGoalAge": "65",
                "customRetirementAge": "",
                "confirmMaturityAge": True,
                "maturityAge": "121",
                "alternateMaturityAge": ""
            }
        }
    }
    
    response = requests.post(
        f"{BASE_URL}/api/scenarios",
        headers=headers,
        json=scenario_data
    )
    
    print(f"Create scenario status: {response.status_code}")
    
    if response.status_code == 201:
        result = response.json()
        scenario_id = result.get('id')
        print(f"✅ Scenario with policy data created: {scenario_id}")
        
        # Verify it can be retrieved
        get_response = requests.get(f"{BASE_URL}/api/scenarios", headers=create_auth_header())
        if get_response.status_code == 200:
            scenarios_data = get_response.json()
            scenarios = scenarios_data.get('scenarios', [])
            
            # Find our scenario
            found_scenario = None
            for scenario in scenarios:
                if scenario.get('id') == scenario_id:
                    found_scenario = scenario
                    break
            
            if found_scenario:
                print(f"✅ Scenario retrieved successfully")
                print(f"   Name: {found_scenario['name']}")
                print(f"   Category: {found_scenario['category']}")
                print(f"   Has policy data: {'Yes' if 'data' in found_scenario else 'No'}")
                return True
            else:
                print(f"❌ Scenario not found after creation")
                return False
        else:
            print(f"❌ Failed to retrieve scenarios")
            return False
    else:
        print(f"❌ Failed to create scenario: {response.text}")
        return False

def main():
    """Run policy persistence tests"""
    print("🚀 Testing Policy Data Persistence")
    print("=" * 50)
    
    tests = [
        ("Policy Search and Data Structure", test_policy_search_and_persistence),
        ("Scenario Creation with Policy Data", test_scenarios_with_policy_data)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    print("\n📊 Test Results Summary:")
    print("=" * 40)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} - {test_name}")
        if not passed:
            all_passed = False
    
    print("\n🎯 Frontend Persistence Status:")
    if all_passed:
        print("✅ Backend is ready for frontend persistence!")
        print("   - Policy search data structure is correct")
        print("   - Scenarios can store detailed policy data")
        print("   - Frontend localStorage persistence has been added")
    else:
        print("❌ Some backend issues detected")
    
    print("\n💡 What's been implemented:")
    print("1. ✅ DashboardContext now saves policy search data to localStorage")
    print("2. ✅ DashboardContext restores policy search data on page load")
    print("3. ✅ As-Is scenarios are saved to backend and persist")
    print("4. ✅ Selected scenarios are saved to backend and persist")
    
    print("\n🔄 To test persistence:")
    print("1. Search for a policy (John Smith, POL-12345678, CUS-567890)")
    print("2. Save an As-Is configuration")
    print("3. Go to Selected Scenarios and select some scenarios")
    print("4. Refresh the page (F5)")
    print("5. Verify all data is still there!")

if __name__ == "__main__":
    main()
