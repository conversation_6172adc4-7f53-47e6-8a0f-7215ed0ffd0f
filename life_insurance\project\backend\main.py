#!/usr/bin/env python3
"""
Life Insurance Application FastAPI Backend
Using FastAPI with SQLite3 built-in database
"""

import sqlite3
import uuid
import hashlib
import json
import os
from datetime import datetime, timezone
from typing import List, Optional
from contextlib import contextmanager

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTT<PERSON>Basic, HTTPBasicCredentials
from pydantic import BaseModel, Field
import uvicorn


# Pydantic Models
class ScenarioBase(BaseModel):
    name: str
    category: str
    asIsDetails: str
    whatIfOptions: List[str] = []


class ScenarioCreate(ScenarioBase):
    pass


class ScenarioUpdate(BaseModel):
    name: Optional[str] = None
    category: Optional[str] = None
    asIsDetails: Optional[str] = None
    whatIfOptions: Optional[List[str]] = None


class Scenario(ScenarioBase):
    id: str
    userId: str
    createdAt: datetime
    updatedAt: datetime

    class Config:
        from_attributes = True


# As-Is Illustration Models
class AsIsIllustrationData(BaseModel):
    policyData: dict
    illustrationScenarios: dict
    projectionResults: Optional[dict] = None


class AsIsIllustrationCreate(BaseModel):
    name: str
    policyNumber: str
    customerName: str
    customerId: str
    data: AsIsIllustrationData


class AsIsIllustrationUpdate(BaseModel):
    name: Optional[str] = None
    data: Optional[AsIsIllustrationData] = None


class AsIsIllustration(BaseModel):
    id: str
    userId: str
    name: str
    policyNumber: str
    customerName: str
    customerId: str
    data: AsIsIllustrationData
    createdAt: datetime
    updatedAt: datetime

    class Config:
        from_attributes = True


class SelectedScenariosRequest(BaseModel):
    selectedScenarios: List[str]


class SelectedScenariosResponse(BaseModel):
    selectedScenarios: List[str]


class HealthResponse(BaseModel):
    status: str
    timestamp: datetime


# Policy-related models
class PolicyDetails(BaseModel):
    id: str
    name: str
    description: str
    coverage: str
    premium: str
    features: List[str]
    issue_date: str
    cash_value: str
    payment_frequency: str
    premium_type: str
    next_due_date: str


class PaymentHistory(BaseModel):
    date: str
    amount: str
    status: str


class TransactionHistory(BaseModel):
    id: str
    date: str
    type: str
    amount: str
    remarks: str


class ActiveRider(BaseModel):
    name: str
    coverage: str
    status: str


class Policy(BaseModel):
    policy_number: str
    policy_type: str
    status: str
    policy_details: PolicyDetails
    payment_history: List[PaymentHistory]
    transaction_history: List[TransactionHistory]
    active_riders: List[ActiveRider]


class Customer(BaseModel):
    customer_id: str
    name: str
    dob: str
    email: str
    phone: str
    address: str
    occupation: str
    annual_income: str
    policies: List[Policy]


class PolicySearchRequest(BaseModel):
    customer_id: Optional[str] = None
    policy_number: Optional[str] = None
    customer_name: Optional[str] = None


class PolicySearchResponse(BaseModel):
    customer: Optional[Customer] = None
    found: bool
    message: str


# Database Manager
class DatabaseManager:
    def __init__(self, db_path: str = "life_insurance.db"):
        self.db_path = db_path
        self.init_database()

    @contextmanager
    def get_db_connection(self):
        """Context manager for database connections"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Enable dict-like access to rows
        try:
            yield conn
        finally:
            conn.close()

    def init_database(self):
        """Initialize database tables"""
        with self.get_db_connection() as conn:
            cursor = conn.cursor()
            
            # Create users table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id TEXT PRIMARY KEY,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    name TEXT NOT NULL,
                    email TEXT,
                    created_at TEXT NOT NULL
                )
            """)
            
            # Create scenarios table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS scenarios (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    name TEXT NOT NULL,
                    category TEXT NOT NULL,
                    as_is_details TEXT NOT NULL,
                    what_if_options TEXT NOT NULL,  -- JSON string
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)
            
            # Create selected_scenarios table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS selected_scenarios (
                    user_id TEXT PRIMARY KEY,
                    selected_ids TEXT NOT NULL,  -- JSON string
                    updated_at TEXT NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)

            # Create as_is_illustrations table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS as_is_illustrations (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    name TEXT NOT NULL,
                    policy_number TEXT NOT NULL,
                    customer_name TEXT NOT NULL,
                    customer_id TEXT NOT NULL,
                    data TEXT NOT NULL,  -- JSON string
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)

            conn.commit()
            
            # Create demo user if not exists
            self.create_demo_user()

    def create_demo_user(self):
        """Create demo user for testing"""
        with self.get_db_connection() as conn:
            cursor = conn.cursor()
            
            # Check if demo user exists
            cursor.execute("SELECT id FROM users WHERE username = ?", ("demo",))
            if cursor.fetchone():
                return
            
            # Create demo user
            user_id = "demo-user-id"
            password_hash = hashlib.sha256("demo123".encode()).hexdigest()
            created_at = datetime.now(timezone.utc).isoformat()
            
            cursor.execute("""
                INSERT INTO users (id, username, password_hash, name, email, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (user_id, "demo", password_hash, "Demo User", "<EMAIL>", created_at))
            
            conn.commit()

    def verify_user(self, username: str, password: str) -> Optional[str]:
        """Verify user credentials and return user_id"""
        password_hash = hashlib.sha256(password.encode()).hexdigest()

        print(f"🔐 Auth attempt: username={username}, password_hash={password_hash[:10]}...")

        with self.get_db_connection() as conn:
            cursor = conn.cursor()

            # Debug: Check if user exists
            cursor.execute("SELECT username, password_hash FROM users WHERE username = ?", (username,))
            user_check = cursor.fetchone()
            if user_check:
                print(f"🔍 Found user: {user_check['username']}, stored_hash={user_check['password_hash'][:10]}...")
            else:
                print(f"❌ User {username} not found in database")
                # List all users for debugging
                cursor.execute("SELECT username FROM users")
                all_users = cursor.fetchall()
                print(f"📋 Available users: {[u['username'] for u in all_users]}")

            cursor.execute(
                "SELECT id FROM users WHERE username = ? AND password_hash = ?",
                (username, password_hash)
            )
            result = cursor.fetchone()

            if result:
                print(f"✅ Authentication successful for {username}")
            else:
                print(f"❌ Authentication failed for {username}")

            return result["id"] if result else None


# Policy Data Manager
class PolicyDataManager:
    def __init__(self, data_file: str = "data/policy_data.json"):
        self.data_file = data_file
        self.policy_data = self.load_policy_data()

    def load_policy_data(self) -> dict:
        """Load policy data from JSON file"""
        try:
            print(f"📂 Loading policy data from: {self.data_file}")
            with open(self.data_file, 'r') as f:
                data = json.load(f)
                print(f"✅ Loaded {len(data.get('customers', {}))} customers")
                return data
        except FileNotFoundError:
            print(f"❌ Warning: Policy data file {self.data_file} not found")
            return {"customers": {}}
        except json.JSONDecodeError:
            print(f"❌ Warning: Invalid JSON in {self.data_file}")
            return {"customers": {}}

    def search_customer(self, customer_id: str = None, policy_number: str = None, customer_name: str = None) -> Optional[Customer]:
        """Search for customer by ID, policy number, or name"""
        customers = self.policy_data.get("customers", {})

        # Search by customer ID first
        if customer_id:
            customer_data = customers.get(customer_id)
            if customer_data:
                return Customer(**customer_data)

        # Search by policy number or customer name across all customers
        for customer_data in customers.values():
            # Check customer name match
            if customer_name and customer_data.get("name", "").lower() == customer_name.lower():
                return Customer(**customer_data)

            # Check policy number match
            if policy_number:
                for policy in customer_data.get("policies", []):
                    if policy.get("policy_number") == policy_number:
                        return Customer(**customer_data)

        return None

    def get_customer_by_composite_key(self, customer_id: str, policy_number: str, customer_name: str) -> Optional[Customer]:
        """Get customer by composite key (matching frontend logic)"""
        customers = self.policy_data.get("customers", {})

        # First try exact customer ID match
        customer_data = customers.get(customer_id)
        if customer_data:
            # Verify name matches
            if customer_data.get("name", "").lower() == customer_name.lower():
                # Verify policy exists
                for policy in customer_data.get("policies", []):
                    if policy.get("policy_number") == policy_number:
                        return Customer(**customer_data)

        return None


# Initialize FastAPI app
app = FastAPI(
    title="Life Insurance Application API",
    description="FastAPI backend with SQLite3 for Life Insurance scenarios",
    version="2.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize database and policy data
db_manager = DatabaseManager()
policy_manager = PolicyDataManager()

# Security
security = HTTPBasic()


def get_current_user(credentials: HTTPBasicCredentials = Depends(security)) -> str:
    """Authenticate user and return user_id"""
    user_id = db_manager.verify_user(credentials.username, credentials.password)
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Basic"},
        )
    return user_id


# API Endpoints
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now(timezone.utc)
    )


@app.get("/api/scenarios", response_model=dict)
async def get_scenarios(user_id: str = Depends(get_current_user)):
    """Get all scenarios for the authenticated user"""
    import json
    
    with db_manager.get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT id, name, category, as_is_details, what_if_options, created_at, updated_at
            FROM scenarios 
            WHERE user_id = ?
            ORDER BY created_at DESC
        """, (user_id,))
        
        scenarios = []
        for row in cursor.fetchall():
            scenario = {
                "id": row["id"],
                "userId": user_id,
                "name": row["name"],
                "category": row["category"],
                "asIsDetails": row["as_is_details"],
                "whatIfOptions": json.loads(row["what_if_options"]),
                "createdAt": row["created_at"],
                "updatedAt": row["updated_at"]
            }
            scenarios.append(scenario)
        
        return {"scenarios": scenarios}


@app.post("/api/scenarios", status_code=201)
async def create_scenario(
    scenario: ScenarioCreate,
    user_id: str = Depends(get_current_user)
):
    """Create a new scenario"""
    import json
    
    scenario_id = str(uuid.uuid4())
    now = datetime.now(timezone.utc).isoformat()
    
    with db_manager.get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO scenarios (id, user_id, name, category, as_is_details, what_if_options, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            scenario_id, user_id, scenario.name, scenario.category,
            scenario.asIsDetails, json.dumps(scenario.whatIfOptions),
            now, now
        ))
        conn.commit()
    
    return {"message": "Scenario created successfully", "id": scenario_id}


@app.put("/api/scenarios/{scenario_id}")
async def update_scenario(
    scenario_id: str,
    updates: ScenarioUpdate,
    user_id: str = Depends(get_current_user)
):
    """Update an existing scenario"""
    import json
    
    # Build update query dynamically
    update_fields = []
    values = []
    
    if updates.name is not None:
        update_fields.append("name = ?")
        values.append(updates.name)
    
    if updates.category is not None:
        update_fields.append("category = ?")
        values.append(updates.category)
    
    if updates.asIsDetails is not None:
        update_fields.append("as_is_details = ?")
        values.append(updates.asIsDetails)
    
    if updates.whatIfOptions is not None:
        update_fields.append("what_if_options = ?")
        values.append(json.dumps(updates.whatIfOptions))
    
    if not update_fields:
        raise HTTPException(status_code=400, detail="No fields to update")
    
    update_fields.append("updated_at = ?")
    values.append(datetime.now(timezone.utc).isoformat())
    
    values.extend([scenario_id, user_id])
    
    with db_manager.get_db_connection() as conn:
        cursor = conn.cursor()
        query = f"UPDATE scenarios SET {', '.join(update_fields)} WHERE id = ? AND user_id = ?"
        cursor.execute(query, values)
        
        if cursor.rowcount == 0:
            raise HTTPException(status_code=404, detail="Scenario not found")
        
        conn.commit()
    
    return {"message": "Scenario updated successfully"}


@app.delete("/api/scenarios/{scenario_id}")
async def delete_scenario(
    scenario_id: str,
    user_id: str = Depends(get_current_user)
):
    """Delete a scenario"""
    with db_manager.get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("DELETE FROM scenarios WHERE id = ? AND user_id = ?", (scenario_id, user_id))

        if cursor.rowcount == 0:
            raise HTTPException(status_code=404, detail="Scenario not found")

        conn.commit()

    return {"message": "Scenario deleted successfully"}


@app.get("/api/scenarios/selected", response_model=SelectedScenariosResponse)
async def get_selected_scenarios(user_id: str = Depends(get_current_user)):
    """Get selected scenario IDs for the authenticated user"""
    import json

    with db_manager.get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT selected_ids FROM selected_scenarios WHERE user_id = ?", (user_id,))
        result = cursor.fetchone()

        if result:
            selected_ids = json.loads(result["selected_ids"])
        else:
            selected_ids = []

        return SelectedScenariosResponse(selectedScenarios=selected_ids)


@app.post("/api/scenarios/selected")
async def update_selected_scenarios(
    request: SelectedScenariosRequest,
    user_id: str = Depends(get_current_user)
):
    """Update selected scenario IDs for the authenticated user"""
    import json

    selected_ids_json = json.dumps(request.selectedScenarios)
    now = datetime.now(timezone.utc).isoformat()

    with db_manager.get_db_connection() as conn:
        cursor = conn.cursor()

        # Use INSERT OR REPLACE to handle both insert and update
        cursor.execute("""
            INSERT OR REPLACE INTO selected_scenarios (user_id, selected_ids, updated_at)
            VALUES (?, ?, ?)
        """, (user_id, selected_ids_json, now))

        conn.commit()

    return {"message": "Selected scenarios updated successfully"}


@app.post("/api/policies/search", response_model=PolicySearchResponse)
async def search_policies(
    search_request: PolicySearchRequest,
    user_id: str = Depends(get_current_user)
):
    """Search for customer policies"""
    try:
        print(f"🔍 Policy search request: {search_request}")
        customer = None

        # If all three fields are provided, use composite key search (matching frontend logic)
        if search_request.customer_id and search_request.policy_number and search_request.customer_name:
            print(f"🔑 Using composite key search")
            customer = policy_manager.get_customer_by_composite_key(
                search_request.customer_id,
                search_request.policy_number,
                search_request.customer_name
            )
        else:
            print(f"🔍 Using flexible search")
            # Otherwise, use flexible search
            customer = policy_manager.search_customer(
                customer_id=search_request.customer_id,
                policy_number=search_request.policy_number,
                customer_name=search_request.customer_name
            )

        if customer:
            print(f"✅ Customer found: {customer.name}")
            return PolicySearchResponse(
                customer=customer,
                found=True,
                message="Customer found successfully"
            )
        else:
            print(f"❌ Customer not found")
            return PolicySearchResponse(
                customer=None,
                found=False,
                message="Customer not found. Please check your details."
            )

    except Exception as e:
        print(f"💥 Error in policy search: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error during policy search"
        )


@app.get("/api/policies/customer/{customer_id}", response_model=Customer)
async def get_customer_policies(
    customer_id: str,
    user_id: str = Depends(get_current_user)
):
    """Get all policies for a specific customer"""
    try:
        customer = policy_manager.search_customer(customer_id=customer_id)

        if not customer:
            raise HTTPException(
                status_code=404,
                detail="Customer not found"
            )

        return customer

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error getting customer policies: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error while retrieving customer policies"
        )


# As-Is Illustration Endpoints
@app.get("/api/as-is-illustrations", response_model=dict)
async def get_as_is_illustrations(user_id: str = Depends(get_current_user)):
    """Get all As-Is illustrations for the authenticated user"""
    import json

    with db_manager.get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT id, name, policy_number, customer_name, customer_id, data, created_at, updated_at
            FROM as_is_illustrations
            WHERE user_id = ?
            ORDER BY created_at DESC
        """, (user_id,))

        illustrations = []
        for row in cursor.fetchall():
            illustration = {
                "id": row["id"],
                "userId": user_id,
                "name": row["name"],
                "policyNumber": row["policy_number"],
                "customerName": row["customer_name"],
                "customerId": row["customer_id"],
                "data": json.loads(row["data"]),
                "createdAt": row["created_at"],
                "updatedAt": row["updated_at"]
            }
            illustrations.append(illustration)

        return {"illustrations": illustrations}


@app.post("/api/as-is-illustrations", status_code=201)
async def create_as_is_illustration(
    illustration: AsIsIllustrationCreate,
    user_id: str = Depends(get_current_user)
):
    """Create a new As-Is illustration"""
    import json

    illustration_id = str(uuid.uuid4())
    now = datetime.now(timezone.utc).isoformat()

    with db_manager.get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO as_is_illustrations (id, user_id, name, policy_number, customer_name, customer_id, data, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            illustration_id, user_id, illustration.name, illustration.policyNumber,
            illustration.customerName, illustration.customerId, json.dumps(illustration.data.dict()),
            now, now
        ))
        conn.commit()

    return {"message": "As-Is illustration created successfully", "id": illustration_id}


@app.put("/api/as-is-illustrations/{illustration_id}")
async def update_as_is_illustration(
    illustration_id: str,
    updates: AsIsIllustrationUpdate,
    user_id: str = Depends(get_current_user)
):
    """Update an existing As-Is illustration"""
    import json

    # Build update query dynamically
    update_fields = []
    values = []

    if updates.name is not None:
        update_fields.append("name = ?")
        values.append(updates.name)

    if updates.data is not None:
        update_fields.append("data = ?")
        values.append(json.dumps(updates.data.dict()))

    if not update_fields:
        raise HTTPException(status_code=400, detail="No fields to update")

    update_fields.append("updated_at = ?")
    values.append(datetime.now(timezone.utc).isoformat())

    values.extend([illustration_id, user_id])

    with db_manager.get_db_connection() as conn:
        cursor = conn.cursor()
        query = f"UPDATE as_is_illustrations SET {', '.join(update_fields)} WHERE id = ? AND user_id = ?"
        cursor.execute(query, values)

        if cursor.rowcount == 0:
            raise HTTPException(status_code=404, detail="As-Is illustration not found")

        conn.commit()

    return {"message": "As-Is illustration updated successfully"}


@app.delete("/api/as-is-illustrations/{illustration_id}")
async def delete_as_is_illustration(
    illustration_id: str,
    user_id: str = Depends(get_current_user)
):
    """Delete an As-Is illustration"""
    with db_manager.get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("DELETE FROM as_is_illustrations WHERE id = ? AND user_id = ?", (illustration_id, user_id))

        if cursor.rowcount == 0:
            raise HTTPException(status_code=404, detail="As-Is illustration not found")

        conn.commit()

    return {"message": "As-Is illustration deleted successfully"}


@app.get("/api/as-is-illustrations/{illustration_id}", response_model=AsIsIllustration)
async def get_as_is_illustration(
    illustration_id: str,
    user_id: str = Depends(get_current_user)
):
    """Get a specific As-Is illustration"""
    import json

    with db_manager.get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT id, name, policy_number, customer_name, customer_id, data, created_at, updated_at
            FROM as_is_illustrations
            WHERE id = ? AND user_id = ?
        """, (illustration_id, user_id))

        row = cursor.fetchone()
        if not row:
            raise HTTPException(status_code=404, detail="As-Is illustration not found")

        return AsIsIllustration(
            id=row["id"],
            userId=user_id,
            name=row["name"],
            policyNumber=row["policy_number"],
            customerName=row["customer_name"],
            customerId=row["customer_id"],
            data=json.loads(row["data"]),
            createdAt=datetime.fromisoformat(row["created_at"]),
            updatedAt=datetime.fromisoformat(row["updated_at"])
        )


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
